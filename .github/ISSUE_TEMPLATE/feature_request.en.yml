name: Feature Request
description: File a feature request
body:
  - type: checkboxes
    id: preface
    attributes:
      label: Prerequisites
      description: Hello! Thank you for submitting a new feature suggestion for Halo. Before we begin, we highly recommend reading through the [Open Source Guides](https://opensource.guide/), which will greatly improve our mutual efficiency.
      options:
        - label: I have searched for related issues in the [Issues](https://github.com/halo-dev/halo/issues) list.
          required: true
        - label: This is a feature related to Halo. If it is not an issue with the project itself, it is recommended to submit it in the [Discussions](https://github.com/halo-dev/halo/discussions).
          required: true
        - label: If it is a feature suggestion for plugins and themes, please submit it in the respective plugin and theme repositories.
          required: true
  - type: markdown
    id: environment
    attributes:
      value: "## Environment"
  - type: input
    id: version
    attributes:
      label: "Your current Halo version"
  - type: markdown
    id: details
    attributes:
      value: "## Details"
  - type: textarea
    id: description
    attributes:
      label: "Describe this feature"
      description: "For ease of management, please do not submit multiple unrelated features under the same issue."
    validations:
      required: true
  - type: textarea
    id: additional-information
    attributes:
      label: "Additional information"
      description: "If you have other information to note, you can fill it in here (screenshots, videos, etc.)."