name: Setup Environment
description: Setup environment to check and build Halo, including console and core projects.

inputs:
  node-version:
    description: Node.js version.
    required: false
    default: "20"

  pnpm-version:
    description: pnpm version.
    required: false
    default: "10"

  java-version:
    description: Java version.
    required: false
    default: "21"

runs:
  using: "composite"
  steps:
    - uses: pnpm/action-setup@v3
      name: Setup pnpm
      with:
        version: ${{ inputs.pnpm-version }}

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}
        cache: "pnpm"
        cache-dependency-path: "ui/pnpm-lock.yaml"

    - name: Setup JDK
      uses: actions/setup-java@v4
      with:
        distribution: "temurin"
        cache: "gradle"
        java-version: ${{ inputs.java-version }}
