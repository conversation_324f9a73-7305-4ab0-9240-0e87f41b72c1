name: Halo Workflow

on:
  pull_request:
    branches:
      - main
      - release-*
    paths:
      - "**"
      - "!**.md"
  push:
    branches:
      - main
      - release-*
    paths:
      - "**"
      - "!**.md"
  release:
    types:
      - published

concurrency: 
  group: ${{github.workflow}} - ${{github.ref}}
  cancel-in-progress: true

jobs:
  test:
    if: github.event_name == 'pull_request' || github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Environment
        uses: ./.github/actions/setup-env
      - name: Check Halo
        run: ./gradlew check
      - name: Upload coverage reports to Codecov
        if: github.repository == 'halo-dev/halo'
        uses: codecov/codecov-action@v4

  build:
    runs-on: ubuntu-latest
    if: always() && (needs.test.result == 'skipped' || needs.test.result == 'success')
    needs: test
    steps:
      - uses: actions/checkout@v4
      - name: Setup Environment
        uses: ./.github/actions/setup-env
      - name: Reset version of Halo
        if: github.event_name == 'release'
        shell: bash
        run: |
          # Set the version with tag name when releasing
          version=${{ github.event.release.tag_name }}
          version=${version#v}
          sed -i "s/version=.*-SNAPSHOT$/version=$version/1" gradle.properties
      - name: Build Halo
        run: ./gradlew clean && ./gradlew downloadPluginPresets && ./gradlew build -x check
      - name: Upload Artifacts
        if: github.repository == 'halo-dev/halo'
        uses: actions/upload-artifact@v4
        with:
          name: halo-artifacts
          path: application/build/libs
          retention-days: 1

  github-release:
    runs-on: ubuntu-latest
    if: always() && needs.build.result == 'success' && github.event_name == 'release'
    needs: build
    steps:
      - uses: actions/checkout@v4
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          name: halo-artifacts
          path: application/build/libs
      - name: Upload Artifacts
        if: github.repository == 'halo-dev/halo'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: gh release upload ${{ github.event.release.tag_name }} application/build/libs/*

  docker-build-and-push:
    if: always() && needs.build.result == 'success' && (github.event_name == 'push' || github.event_name == 'release')
    runs-on: ubuntu-latest
    needs: build
    steps:
      - uses: actions/checkout@v4
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          name: halo-artifacts
          path: application/build/libs
      - name: Docker Buildx and Push
        uses: ./.github/actions/docker-buildx-push
        with:
          image-name: ${{ github.event_name == 'release' && 'halo' || 'halo-dev' }}
          ghcr-token: ${{ secrets.GITHUB_TOKEN }}
          dockerhub-user: ${{ secrets.DOCKER_USERNAME }}
          dockerhub-token: ${{ secrets.DOCKER_TOKEN }}
          f2c-registry-user: ${{ secrets.F2C_REGISTRY_USER }}
          f2c-registry-token: ${{ secrets.F2C_REGISTRY_TOKEN }}
          push: true
          platforms: linux/amd64,linux/arm64/v8,linux/ppc64le,linux/s390x

  e2e-test:
    if: always() && needs.build.result == 'success' && (github.event_name == 'pull_request' || github.event_name == 'push')
    runs-on: ubuntu-latest
    needs: build
    steps:
      - uses: actions/checkout@v4
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          name: halo-artifacts
          path: application/build/libs
      - name: Docker Build
        uses: docker/build-push-action@v5
        with:
          tags: ghcr.io/halo-dev/halo-dev:main
          push: false
          context: .
      - name: E2E Testing
        continue-on-error: true
        run: |
          sudo curl -L https://github.com/docker/compose/releases/download/v2.23.0/docker-compose-linux-x86_64 -o /usr/local/bin/docker-compose
          sudo chmod u+x /usr/local/bin/docker-compose
          cd e2e && make all
