# Security Policy

## Supported Versions

Halo currently supports the versions listed below, where as:

- :white_check_mark: indicates an active development roadmap, is therefore maintaining, and **will** receive Security
  Vulnerability Report.
- :x: indicates such version has already deprecated and **will not** be receiving Security Vulnerability Report.

| Version | Supported          |
| ------- | ------------------ |
| 0.x     | :x:                |
| 1.x     | :x:                |
| 2.x     | :white_check_mark: |

## Reporting a Vulnerability

We first appreciate and are very thankful that you've found a vulnerability issue in Halo! By disclosing such issue to
Halo development team you are helping Halo to become a much more safer project than before! ;)

To protect the existing users of Halo, we kindly ask you to not disclose the vulnerability to anyone except the Halo
development team before a fix has been rolled out.

To Report a Vulnerability, please complete the form below, and send such report by email to `<EMAIL>`.

```
Vulnerability has been observed in...
  - Docker? [n/y]: 
    if yes for the question above,
    - `docker -v`: 
    - `docker images halohub/halo`: 
  
  - by `java -jar halo.jar`? [n/y]: 
    if yes for the question above,
    - `uname -a`: 
    - `java -version`: 
 
- Affected by Halo version(s) [e.g. v2.4.0]: 
- Vulnerability self-scoring [1-10]: 
- Would you like to be attributed? (Whether you agree us to appreciate you by putting your name in the CHANGELOG of the next fix release) [n/y]: 
```
