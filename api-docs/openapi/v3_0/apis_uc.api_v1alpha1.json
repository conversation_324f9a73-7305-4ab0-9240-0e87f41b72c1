{"openapi": "3.0.1", "info": {"title": "Halo", "version": "2.21.0-SNAPSHOT"}, "servers": [{"url": "http://localhost:8091", "description": "Generated server url"}], "security": [{"basicAuth": [], "bearerAuth": []}], "paths": {"/apis/api.notification.halo.run/v1alpha1/notifiers/{name}/receiver-config": {"get": {"description": "Fetch receiver config of notifier", "operationId": "FetchReceiverConfig", "parameters": [{"description": "Notifier name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"type": "object"}}}, "description": "default response"}}, "tags": ["NotifierV1alpha1Uc"]}, "post": {"description": "Save receiver config of notifier", "operationId": "SaveReceiverConfig", "parameters": [{"description": "Notifier name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}}}, "required": true}, "responses": {"default": {"content": {}, "description": "default response"}}, "tags": ["NotifierV1alpha1Uc"]}}, "/apis/api.notification.halo.run/v1alpha1/userspaces/{username}/notification-preferences": {"get": {"description": "List notification preferences for the authenticated user.", "operationId": "ListUserNotificationPreferences", "parameters": [{"description": "Username", "in": "path", "name": "username", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReasonTypeNotifierMatrix"}}}, "description": "default response"}}, "tags": ["NotificationV1alpha1Uc"]}, "post": {"description": "Save notification preferences for the authenticated user.", "operationId": "SaveUserNotificationPreferences", "parameters": [{"description": "Username", "in": "path", "name": "username", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReasonTypeNotifierCollectionRequest"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ReasonTypeNotifierMatrix"}}}, "description": "default response"}}, "tags": ["NotificationV1alpha1Uc"]}}, "/apis/api.notification.halo.run/v1alpha1/userspaces/{username}/notifications": {"get": {"description": "List notifications for the authenticated user.", "operationId": "ListUserNotifications", "parameters": [{"description": "Username", "in": "path", "name": "username", "required": true, "schema": {"type": "string"}}, {"description": "Page number. De<PERSON><PERSON> is 0.", "in": "query", "name": "page", "schema": {"type": "integer", "format": "int32"}}, {"description": "Size number. De<PERSON>ult is 0.", "in": "query", "name": "size", "schema": {"type": "integer", "format": "int32"}}, {"description": "Label selector. e.g.: hidden!=true", "in": "query", "name": "labelSelector", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Field selector. e.g.: metadata.name==halo", "in": "query", "name": "fieldSelector", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/NotificationList"}}}, "description": "default response"}}, "tags": ["NotificationV1alpha1Uc"]}}, "/apis/api.notification.halo.run/v1alpha1/userspaces/{username}/notifications/-/mark-specified-as-read": {"put": {"description": "Mark the specified notifications as read.", "operationId": "MarkNotificationsAsRead", "parameters": [{"description": "Username", "in": "path", "name": "username", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkSpecifiedRequest"}}}, "required": true}, "responses": {"default": {"content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}}}}, "description": "default response"}}, "tags": ["NotificationV1alpha1Uc"]}}, "/apis/api.notification.halo.run/v1alpha1/userspaces/{username}/notifications/{name}": {"delete": {"description": "Delete the specified notification.", "operationId": "DeleteSpecifiedNotification", "parameters": [{"description": "Username", "in": "path", "name": "username", "required": true, "schema": {"type": "string"}}, {"description": "Notification name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Notification"}}}, "description": "default response"}}, "tags": ["NotificationV1alpha1Uc"]}}, "/apis/api.notification.halo.run/v1alpha1/userspaces/{username}/notifications/{name}/mark-as-read": {"put": {"description": "Mark the specified notification as read.", "operationId": "MarkNotificationAsRead", "parameters": [{"description": "Username", "in": "path", "name": "username", "required": true, "schema": {"type": "string"}}, {"description": "Notification name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Notification"}}}, "description": "default response"}}, "tags": ["NotificationV1alpha1Uc"]}}, "/apis/uc.api.auth.halo.run/v1alpha1/user-connections/{registerId}/disconnect": {"put": {"description": "Disconnect my connection from a third-party platform.", "operationId": "DisconnectMyConnection", "parameters": [{"description": "The registration ID of the third-party platform.", "in": "path", "name": "registerId", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserConnection"}}}}, "description": "default response"}}, "tags": ["UserConnectionV1alpha1Uc"]}}, "/apis/uc.api.content.halo.run/v1alpha1/posts": {"get": {"description": "List posts owned by the current user.", "operationId": "ListMyPosts", "parameters": [{"description": "Page number. De<PERSON><PERSON> is 0.", "in": "query", "name": "page", "schema": {"type": "integer", "format": "int32"}}, {"description": "Size number. De<PERSON>ult is 0.", "in": "query", "name": "size", "schema": {"type": "integer", "format": "int32"}}, {"description": "Label selector. e.g.: hidden!=true", "in": "query", "name": "labelSelector", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Field selector. e.g.: metadata.name==halo", "in": "query", "name": "fieldSelector", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Posts filtered by publish phase.", "in": "query", "name": "publishPhase", "schema": {"type": "string", "enum": ["DRAFT", "PENDING_APPROVAL", "PUBLISHED", "FAILED"]}}, {"description": "Posts filtered by keyword.", "in": "query", "name": "keyword", "schema": {"type": "string"}}, {"description": "Posts filtered by category including sub-categories.", "in": "query", "name": "categoryWithChildren", "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListedPostList"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}, "post": {"description": "Create my post. If you want to create a post with content, please set\n annotation: \"content.halo.run/content-json\" into annotations and refer\n to Content for corresponding data type.\n", "operationId": "CreateMyPost", "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}}, "/apis/uc.api.content.halo.run/v1alpha1/posts/{name}": {"get": {"description": "Get post that belongs to the current user.", "operationId": "GetMyPost", "parameters": [{"description": "Post name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}, "put": {"description": "Update my post.", "operationId": "UpdateMyPost", "parameters": [{"description": "Post name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}}, "/apis/uc.api.content.halo.run/v1alpha1/posts/{name}/draft": {"get": {"description": "Get my post draft.", "operationId": "GetMyPostDraft", "parameters": [{"description": "Post name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}, {"description": "Should include patched content and raw or not.", "in": "query", "name": "patched", "schema": {"type": "boolean"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Snapshot"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}, "put": {"description": "Update draft of my post. Please make sure set annotation:\n\"content.halo.run/content-json\" into annotations and refer to\nContent for corresponding data type.\n", "operationId": "UpdateMyPostDraft", "parameters": [{"description": "Post name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Snapshot"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Snapshot"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}}, "/apis/uc.api.content.halo.run/v1alpha1/posts/{name}/publish": {"put": {"description": "Publish my post.", "operationId": "PublishMyPost", "parameters": [{"description": "Post name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}}, "/apis/uc.api.content.halo.run/v1alpha1/posts/{name}/recycle": {"delete": {"description": "Move my post to recycle bin.", "operationId": "RecycleMyPost", "parameters": [{"description": "Post name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}}, "/apis/uc.api.content.halo.run/v1alpha1/posts/{name}/unpublish": {"put": {"description": "Unpublish my post.", "operationId": "UnpublishMyPost", "parameters": [{"description": "Post name", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Post"}}}, "description": "default response"}}, "tags": ["PostV1alpha1Uc"]}}, "/apis/uc.api.content.halo.run/v1alpha1/snapshots/{name}": {"get": {"description": "Get snapshot for one post.", "operationId": "GetSnapshotForPost", "parameters": [{"description": "Snapshot name.", "in": "path", "name": "name", "required": true, "schema": {"type": "string"}}, {"description": "Post name.", "in": "query", "name": "postName", "required": true, "schema": {"type": "string"}}, {"description": "Should include patched content and raw or not.", "in": "query", "name": "patched", "schema": {"type": "boolean"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Snapshot"}}}, "description": "default response"}}, "tags": ["SnapshotV1alpha1Uc"]}}, "/apis/uc.api.halo.run/v1alpha1/user-preferences/{group}": {"get": {"description": "Get my preference by group.", "operationId": "getMyPreference", "parameters": [{"description": "Group of user preference, e.g. `notification`.", "in": "path", "name": "group", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/JsonNode"}}}, "description": "default response"}}, "tags": ["UserPreferenceV1alpha1Uc"]}, "put": {"description": "Create or update my preference by group.", "operationId": "updateMyPreference", "parameters": [{"description": "Group of user preference, e.g. `notification`.", "in": "path", "name": "group", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/JsonNode"}}}, "required": true}, "responses": {"204": {"description": "No content, preference updated successfully."}}, "tags": ["UserPreferenceV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/authentications/two-factor/settings": {"get": {"description": "Get Two-factor authentication settings.", "operationId": "GetTwoFactorAuthenticationSettings", "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/TwoFactorAuthSettings"}}}, "description": "default response"}}, "tags": ["TwoFactorAuthV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/authentications/two-factor/settings/disabled": {"put": {"description": "Disable Two-factor authentication", "operationId": "DisableTwoFactor", "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/PasswordRequest"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/TwoFactorAuthSettings"}}}, "description": "default response"}}, "tags": ["TwoFactorAuthV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/authentications/two-factor/settings/enabled": {"put": {"description": "Enable Two-factor authentication", "operationId": "EnableTwoFactor", "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/PasswordRequest"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/TwoFactorAuthSettings"}}}, "description": "default response"}}, "tags": ["TwoFactorAuthV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/authentications/two-factor/totp": {"post": {"description": "Configure a TOTP", "operationId": "ConfigurerTotp", "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/TotpRequest"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/TwoFactorAuthSettings"}}}, "description": "default response"}}, "tags": ["TwoFactorAuthV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/authentications/two-factor/totp/-": {"delete": {"operationId": "DeleteTotp", "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/PasswordRequest"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/TwoFactorAuthSettings"}}}, "description": "default response"}}, "tags": ["TwoFactorAuthV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/authentications/two-factor/totp/auth-link": {"get": {"description": "Get TOTP auth link, including secret", "operationId": "GetTotpAuthLink", "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/TotpAuthLinkResponse"}}}, "description": "default response"}}, "tags": ["TwoFactorAuthV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/devices": {"get": {"description": "List all user devices", "operationId": "ListDevices", "responses": {"default": {"content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserDevice"}}}}, "description": "default response"}}, "tags": ["DeviceV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/devices/{deviceId}": {"delete": {"description": "Revoke a own device", "operationId": "RevokeDevice", "parameters": [{"description": "Device ID", "in": "path", "name": "deviceId", "required": true, "schema": {"type": "string"}}], "responses": {"204 NO_CONTENT": {"description": "default response"}}, "tags": ["DeviceV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens": {"get": {"description": "Obtain PAT list.", "operationId": "ObtainPats", "responses": {"default": {"content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PersonalAccessToken"}}}}, "description": "default response"}}, "tags": ["PersonalAccessTokenV1alpha1Uc"]}, "post": {"description": "Generate a PAT.", "operationId": "GeneratePat", "requestBody": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/PersonalAccessToken"}}}, "required": true}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/PersonalAccessToken"}}}, "description": "default response"}}, "tags": ["PersonalAccessTokenV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens/{name}": {"delete": {"description": "Delete a PAT", "operationId": "DeletePat", "parameters": [{"in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {}, "tags": ["PersonalAccessTokenV1alpha1Uc"]}, "get": {"description": "Obtain a PAT.", "operationId": "ObtainPat", "parameters": [{"in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {}, "tags": ["PersonalAccessTokenV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens/{name}/actions/restoration": {"put": {"description": "Restore a PAT.", "operationId": "RestorePat", "parameters": [{"in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {}, "tags": ["PersonalAccessTokenV1alpha1Uc"]}}, "/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens/{name}/actions/revocation": {"put": {"description": "Revoke a PAT", "operationId": "RevokePat", "parameters": [{"in": "path", "name": "name", "required": true, "schema": {"type": "string"}}], "responses": {}, "tags": ["PersonalAccessTokenV1alpha1Uc"]}}, "/apis/uc.api.storage.halo.run/v1alpha1/attachments": {"get": {"description": "List attachments of the current user uploaded.", "operationId": "ListMyAttachments", "parameters": [{"description": "Page number. De<PERSON><PERSON> is 0.", "in": "query", "name": "page", "schema": {"type": "integer", "format": "int32"}}, {"description": "Size number. De<PERSON>ult is 0.", "in": "query", "name": "size", "schema": {"type": "integer", "format": "int32"}}, {"description": "Label selector. e.g.: hidden!=true", "in": "query", "name": "labelSelector", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Field selector. e.g.: metadata.name==halo", "in": "query", "name": "fieldSelector", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.", "in": "query", "name": "sort", "schema": {"type": "array", "items": {"type": "string"}}}, {"description": "Filter attachments without group. This parameter will ignore group parameter.", "in": "query", "name": "ungrouped", "schema": {"type": "boolean"}}, {"description": "Keyword for searching.", "in": "query", "name": "keyword", "schema": {"type": "string"}}, {"description": "Acceptable media types.", "in": "query", "name": "accepts", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/AttachmentList"}}}, "description": "default response"}}, "tags": ["AttachmentV1alpha1Uc"]}, "post": {"description": "Create attachment for the given post.", "operationId": "CreateAttachmentForPost", "parameters": [{"description": "Wait for permalink.", "in": "query", "name": "waitForPermalink", "schema": {"type": "boolean"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/PostAttachmentRequest"}}}}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Attachment"}}}, "description": "default response"}}, "tags": ["AttachmentV1alpha1Uc"]}}, "/apis/uc.api.storage.halo.run/v1alpha1/attachments/-/upload": {"post": {"description": "Upload attachment to user center storage.", "operationId": "UploadUcAttachment", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/UcUploadRequest"}}}, "required": true}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Attachment"}}}, "description": "default response"}}, "tags": ["AttachmentV1alpha1Uc"]}}, "/apis/uc.api.storage.halo.run/v1alpha1/attachments/-/upload-from-url": {"post": {"description": "Upload attachment from the given URL.", "operationId": "ExternalTransferAttachment_1", "parameters": [{"description": "Wait for permalink.", "in": "query", "name": "waitForPermalink", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UcUploadFromUrlRequest"}}}, "required": true}, "responses": {"default": {"content": {"*/*": {"schema": {"$ref": "#/components/schemas/Attachment"}}}, "description": "default response"}}, "tags": ["AttachmentV1alpha1Uc"]}}}, "components": {"schemas": {"AddOperation": {"required": ["op", "path", "value"], "type": "object", "properties": {"op": {"type": "string", "enum": ["add"]}, "path": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}, "value": {"description": "Value can be any JSON value"}}}, "Attachment": {"required": ["apiVersion", "kind", "metadata", "spec"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/AttachmentSpec"}, "status": {"$ref": "#/components/schemas/AttachmentStatus"}}}, "AttachmentList": {"required": ["first", "hasNext", "has<PERSON>revious", "items", "last", "page", "size", "total", "totalPages"], "type": "object", "properties": {"first": {"type": "boolean", "description": "Indicates whether current page is the first page."}, "hasNext": {"type": "boolean", "description": "Indicates whether current page has previous page."}, "hasPrevious": {"type": "boolean", "description": "Indicates whether current page has previous page."}, "items": {"type": "array", "description": "A chunk of items.", "items": {"$ref": "#/components/schemas/Attachment"}}, "last": {"type": "boolean", "description": "Indicates whether current page is the last page."}, "page": {"type": "integer", "description": "Page number, starts from 1. If not set or equal to 0, it means no pagination.", "format": "int32"}, "size": {"type": "integer", "description": "Size of each page. If not set or equal to 0, it means no pagination.", "format": "int32"}, "total": {"type": "integer", "description": "Total elements.", "format": "int64"}, "totalPages": {"type": "integer", "description": "Indicates total pages.", "format": "int64"}}}, "AttachmentSpec": {"type": "object", "properties": {"displayName": {"type": "string", "description": "Display name of attachment"}, "groupName": {"type": "string", "description": "Group name"}, "mediaType": {"type": "string", "description": "Media type of attachment"}, "ownerName": {"type": "string", "description": "Name of User who uploads the attachment"}, "policyName": {"type": "string", "description": "Policy name"}, "size": {"minimum": 0, "type": "integer", "description": "Size of attachment. Unit is Byte", "format": "int64"}, "tags": {"uniqueItems": true, "type": "array", "description": "Tags of attachment", "items": {"type": "string", "description": "Tag name"}}}}, "AttachmentStatus": {"type": "object", "properties": {"permalink": {"type": "string", "description": "Permalink of attachment.\nIf it is in local storage, the public URL will be set.\nIf it is in s3 storage, the Object URL will be set.\n"}, "thumbnails": {"type": "object", "additionalProperties": {"type": "string"}}}}, "Category": {"required": ["apiVersion", "kind", "metadata", "spec"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/CategorySpec"}, "status": {"$ref": "#/components/schemas/CategoryStatus"}}}, "CategorySpec": {"required": ["displayName", "priority", "slug"], "type": "object", "properties": {"children": {"type": "array", "items": {"type": "string"}}, "cover": {"type": "string"}, "description": {"type": "string"}, "displayName": {"minLength": 1, "type": "string"}, "hideFromList": {"type": "boolean", "description": "<p>Whether to hide the category from the category list.</p>\n <p>When set to true, the category including its subcategories and related posts will\n not be displayed in the category list, but it can still be accessed by permalink.</p>\n <p>Limitation: It only takes effect on the theme-side categorized list and it only\n allows to be set to true on the first level(root node) of categories.</p>"}, "postTemplate": {"maxLength": 255, "type": "string", "description": "<p>Used to specify the template for the posts associated with the category.</p>\n <p>The priority is not as high as that of the post.</p>\n <p>If the post also specifies a template, the post's template will prevail.</p>"}, "preventParentPostCascadeQuery": {"type": "boolean", "description": "<p>if a category is queried for related posts, the default behavior is to\n query all posts under the category including its subcategories, but if this field is\n set to true, cascade query behavior will be terminated here.</p>\n <p>For example, if a category has subcategories A and B, and A has subcategories C and\n D and C marked this field as true, when querying posts under A category,all posts under A\n and B will be queried, but C and D will not be queried.</p>"}, "priority": {"type": "integer", "format": "int32", "default": 0}, "slug": {"minLength": 1, "type": "string"}, "template": {"maxLength": 255, "type": "string"}}}, "CategoryStatus": {"type": "object", "properties": {"permalink": {"type": "string"}, "postCount": {"type": "integer", "description": "包括当前和其下所有层级的文章数量 (depth=max).", "format": "int32"}, "visiblePostCount": {"type": "integer", "description": "包括当前和其下所有层级的已发布且公开的文章数量 (depth=max).", "format": "int32"}}}, "Condition": {"required": ["lastTransitionTime", "status", "type"], "type": "object", "properties": {"lastTransitionTime": {"type": "string", "description": "Last time the condition transitioned from one status to another.", "format": "date-time"}, "message": {"maxLength": 32768, "type": "string", "description": "Human-readable message indicating details about last transition.\n This may be an empty string."}, "reason": {"maxLength": 1024, "pattern": "^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$", "type": "string", "description": "Unique, one-word, CamelCase reason for the condition's last transition."}, "status": {"type": "string", "description": "Status is the status of the condition. Can be True, False, Unknown.", "enum": ["TRUE", "FALSE", "UNKNOWN"]}, "type": {"maxLength": 316, "pattern": "^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$", "type": "string", "description": "type of condition in CamelCase or in foo.example.com/CamelCase.\n example: Ready, Initialized.\n maxLength: 316."}}, "description": "EqualsAndHashCode 排除了lastTransitionTime否则失败时，lastTransitionTime 会被更新\n 导致 equals 为 false，一直被加入队列."}, "Contributor": {"type": "object", "properties": {"avatar": {"type": "string"}, "displayName": {"type": "string"}, "name": {"type": "string"}}, "description": "Contributor from user."}, "CopyOperation": {"required": ["op", "from", "path"], "type": "object", "properties": {"from": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}, "op": {"type": "string", "enum": ["copy"]}, "path": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}}}, "Device": {"required": ["apiVersion", "kind", "metadata", "spec", "status"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/DeviceSpec"}, "status": {"$ref": "#/components/schemas/DeviceStatus"}}}, "DeviceSpec": {"required": ["ip<PERSON><PERSON><PERSON>", "principalName", "sessionId"], "type": "object", "properties": {"ipAddress": {"maxLength": 129, "type": "string"}, "lastAccessedTime": {"type": "string", "format": "date-time"}, "lastAuthenticatedTime": {"type": "string", "format": "date-time"}, "principalName": {"minLength": 1, "type": "string"}, "rememberMeSeriesId": {"type": "string"}, "sessionId": {"minLength": 1, "type": "string"}, "userAgent": {"maxLength": 500, "type": "string"}}}, "DeviceStatus": {"type": "object", "properties": {"browser": {"type": "string"}, "os": {"type": "string"}}}, "Excerpt": {"required": ["autoGenerate"], "type": "object", "properties": {"autoGenerate": {"type": "boolean", "default": true}, "raw": {"type": "string"}}}, "JsonNode": {"type": "object"}, "JsonPatch": {"minItems": 1, "uniqueItems": true, "type": "array", "description": "JSON schema for JSONPatch operations", "items": {"oneOf": [{"$ref": "#/components/schemas/AddOperation"}, {"$ref": "#/components/schemas/ReplaceOperation"}, {"$ref": "#/components/schemas/TestOperation"}, {"$ref": "#/components/schemas/RemoveOperation"}, {"$ref": "#/components/schemas/MoveOperation"}, {"$ref": "#/components/schemas/CopyOperation"}]}}, "ListedPost": {"required": ["categories", "contributors", "owner", "post", "stats", "tags"], "type": "object", "properties": {"categories": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "contributors": {"type": "array", "items": {"$ref": "#/components/schemas/Contributor"}}, "owner": {"$ref": "#/components/schemas/Contributor"}, "post": {"$ref": "#/components/schemas/Post"}, "stats": {"$ref": "#/components/schemas/Stats"}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}}, "description": "A chunk of items."}, "ListedPostList": {"required": ["first", "hasNext", "has<PERSON>revious", "items", "last", "page", "size", "total", "totalPages"], "type": "object", "properties": {"first": {"type": "boolean", "description": "Indicates whether current page is the first page."}, "hasNext": {"type": "boolean", "description": "Indicates whether current page has previous page."}, "hasPrevious": {"type": "boolean", "description": "Indicates whether current page has previous page."}, "items": {"type": "array", "description": "A chunk of items.", "items": {"$ref": "#/components/schemas/ListedPost"}}, "last": {"type": "boolean", "description": "Indicates whether current page is the last page."}, "page": {"type": "integer", "description": "Page number, starts from 1. If not set or equal to 0, it means no pagination.", "format": "int32"}, "size": {"type": "integer", "description": "Size of each page. If not set or equal to 0, it means no pagination.", "format": "int32"}, "total": {"type": "integer", "description": "Total elements.", "format": "int64"}, "totalPages": {"type": "integer", "description": "Indicates total pages.", "format": "int64"}}}, "MarkSpecifiedRequest": {"type": "object", "properties": {"names": {"type": "array", "items": {"type": "string"}}}}, "Metadata": {"required": ["name"], "type": "object", "properties": {"annotations": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Annotations are like key-value format."}, "creationTimestamp": {"type": "string", "description": "Creation timestamp of the Extension.", "format": "date-time", "nullable": true}, "deletionTimestamp": {"type": "string", "description": "Deletion timestamp of the Extension.", "format": "date-time", "nullable": true}, "finalizers": {"uniqueItems": true, "type": "array", "nullable": true, "items": {"type": "string", "nullable": true}}, "generateName": {"type": "string", "description": "The name field will be generated automatically according to the given generateName field"}, "labels": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Labels are like key-value format."}, "name": {"type": "string", "description": "Metadata name"}, "version": {"type": "integer", "description": "Current version of the Extension. It will be bumped up every update.", "format": "int64", "nullable": true}}, "description": "Metadata of Extension."}, "MoveOperation": {"required": ["op", "from", "path"], "type": "object", "properties": {"from": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}, "op": {"type": "string", "enum": ["move"]}, "path": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}}}, "Notification": {"required": ["apiVersion", "kind", "metadata"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/NotificationSpec"}}, "description": "<p>{@link Notification Notification} is a custom extension that used to store notification information for\n inner use, it's on-site notification.</p>\n\n <p>Supports the following operations:</p>\n <ul>\n    <li>Marked as read: {@link NotificationSpec#setUnread(boolean) NotificationSpec#setUnread(boolean)}</li>\n    <li>Get the last read time: {@link NotificationSpec#getLastReadAt NotificationSpec#getLastReadAt()}</li>\n    <li>Filter by recipient: {@link NotificationSpec#getRecipient NotificationSpec#getRecipient()}</li>\n </ul>"}, "NotificationList": {"required": ["first", "hasNext", "has<PERSON>revious", "items", "last", "page", "size", "total", "totalPages"], "type": "object", "properties": {"first": {"type": "boolean", "description": "Indicates whether current page is the first page."}, "hasNext": {"type": "boolean", "description": "Indicates whether current page has previous page."}, "hasPrevious": {"type": "boolean", "description": "Indicates whether current page has previous page."}, "items": {"type": "array", "description": "A chunk of items.", "items": {"$ref": "#/components/schemas/Notification"}}, "last": {"type": "boolean", "description": "Indicates whether current page is the last page."}, "page": {"type": "integer", "description": "Page number, starts from 1. If not set or equal to 0, it means no pagination.", "format": "int32"}, "size": {"type": "integer", "description": "Size of each page. If not set or equal to 0, it means no pagination.", "format": "int32"}, "total": {"type": "integer", "description": "Total elements.", "format": "int64"}, "totalPages": {"type": "integer", "description": "Indicates total pages.", "format": "int64"}}}, "NotificationSpec": {"required": ["htmlContent", "rawContent", "reason", "recipient", "title"], "type": "object", "properties": {"htmlContent": {"type": "string"}, "lastReadAt": {"type": "string", "format": "date-time"}, "rawContent": {"type": "string"}, "reason": {"minLength": 1, "type": "string", "description": "The name of reason"}, "recipient": {"minLength": 1, "type": "string", "description": "The name of user"}, "title": {"minLength": 1, "type": "string"}, "unread": {"type": "boolean"}}}, "NotifierInfo": {"type": "object", "properties": {"description": {"type": "string"}, "displayName": {"type": "string"}, "name": {"type": "string"}}}, "Part": {"type": "object"}, "PasswordRequest": {"required": ["password"], "type": "object", "properties": {"password": {"minLength": 1, "type": "string"}}}, "PatSpec": {"required": ["name", "tokenId", "username"], "type": "object", "properties": {"description": {"type": "string"}, "expiresAt": {"type": "string", "format": "date-time"}, "lastUsed": {"type": "string", "format": "date-time"}, "name": {"type": "string"}, "revoked": {"type": "boolean"}, "revokesAt": {"type": "string", "format": "date-time"}, "roles": {"type": "array", "items": {"type": "string"}}, "scopes": {"type": "array", "items": {"type": "string"}}, "tokenId": {"type": "string"}, "username": {"type": "string"}}}, "PersonalAccessToken": {"required": ["apiVersion", "kind", "metadata"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/PatSpec"}}}, "Post": {"required": ["apiVersion", "kind", "metadata", "spec"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/PostSpec"}, "status": {"$ref": "#/components/schemas/PostStatus"}}, "description": "<p>Post extension.</p>"}, "PostAttachmentRequest": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "postName": {"type": "string", "description": "Post name."}, "singlePageName": {"type": "string", "description": "Single page name."}}}, "PostSpec": {"required": ["allowComment", "deleted", "excerpt", "pinned", "priority", "publish", "slug", "title", "visible"], "type": "object", "properties": {"allowComment": {"type": "boolean", "default": true}, "baseSnapshot": {"type": "string"}, "categories": {"type": "array", "items": {"type": "string"}}, "cover": {"type": "string"}, "deleted": {"type": "boolean", "default": false}, "excerpt": {"$ref": "#/components/schemas/Excerpt"}, "headSnapshot": {"type": "string"}, "htmlMetas": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "string"}}}, "owner": {"type": "string"}, "pinned": {"type": "boolean", "default": false}, "priority": {"type": "integer", "format": "int32", "default": 0}, "publish": {"type": "boolean", "default": false}, "publishTime": {"type": "string", "format": "date-time"}, "releaseSnapshot": {"type": "string", "description": "文章引用到的已发布的内容，用于主题端显示."}, "slug": {"minLength": 1, "type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "template": {"type": "string"}, "title": {"minLength": 1, "type": "string"}, "visible": {"type": "string", "enum": ["PUBLIC", "INTERNAL", "PRIVATE"], "default": "PUBLIC"}}}, "PostStatus": {"type": "object", "properties": {"commentsCount": {"type": "integer", "format": "int32"}, "conditions": {"type": "array", "properties": {"empty": {"type": "boolean"}}, "items": {"$ref": "#/components/schemas/Condition"}}, "contributors": {"type": "array", "items": {"type": "string"}}, "excerpt": {"type": "string"}, "hideFromList": {"type": "boolean", "description": "see {@link Category.CategorySpec#isHideFromList Category.CategorySpec#isHideFromList()}."}, "inProgress": {"type": "boolean"}, "lastModifyTime": {"type": "string", "format": "date-time"}, "observedVersion": {"type": "integer", "format": "int64"}, "permalink": {"type": "string"}, "phase": {"type": "string"}}}, "ReasonTypeInfo": {"type": "object", "properties": {"description": {"type": "string"}, "displayName": {"type": "string"}, "name": {"type": "string"}, "uiPermissions": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}}}, "ReasonTypeNotifierCollectionRequest": {"required": ["reasonTypeNotifiers"], "type": "object", "properties": {"reasonTypeNotifiers": {"type": "array", "items": {"$ref": "#/components/schemas/ReasonTypeNotifierRequest"}}}}, "ReasonTypeNotifierMatrix": {"type": "object", "properties": {"notifiers": {"type": "array", "items": {"$ref": "#/components/schemas/NotifierInfo"}}, "reasonTypes": {"type": "array", "items": {"$ref": "#/components/schemas/ReasonTypeInfo"}}, "stateMatrix": {"type": "array", "items": {"type": "array", "items": {"type": "boolean"}}}}}, "ReasonTypeNotifierRequest": {"type": "object", "properties": {"notifiers": {"type": "array", "items": {"type": "string"}}, "reasonType": {"type": "string"}}}, "Ref": {"required": ["name"], "type": "object", "properties": {"group": {"type": "string", "description": "Extension group"}, "kind": {"type": "string", "description": "Extension kind"}, "name": {"type": "string", "description": "Extension name. This field is mandatory"}, "version": {"type": "string", "description": "Extension version"}}, "description": "Extension reference object. The name is mandatory"}, "RemoveOperation": {"required": ["op", "path"], "type": "object", "properties": {"op": {"type": "string", "enum": ["remove"]}, "path": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}}}, "ReplaceOperation": {"required": ["op", "path", "value"], "type": "object", "properties": {"op": {"type": "string", "enum": ["replace"]}, "path": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}, "value": {"description": "Value can be any JSON value"}}}, "SnapShotSpec": {"required": ["owner", "rawType", "subjectRef"], "type": "object", "properties": {"contentPatch": {"type": "string"}, "contributors": {"uniqueItems": true, "type": "array", "items": {"type": "string"}}, "lastModifyTime": {"type": "string", "format": "date-time"}, "owner": {"minLength": 1, "type": "string"}, "parentSnapshotName": {"type": "string"}, "rawPatch": {"type": "string"}, "rawType": {"maxLength": 50, "minLength": 1, "type": "string", "description": "such as: markdown | html | json | asciidoc | latex."}, "subjectRef": {"$ref": "#/components/schemas/Ref"}}}, "Snapshot": {"required": ["apiVersion", "kind", "metadata", "spec"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/SnapShotSpec"}}}, "Stats": {"type": "object", "properties": {"approvedComment": {"type": "integer", "format": "int32"}, "totalComment": {"type": "integer", "format": "int32"}, "upvote": {"type": "integer", "format": "int32"}, "visit": {"type": "integer", "format": "int32"}}, "description": "Stats value object."}, "Tag": {"required": ["apiVersion", "kind", "metadata", "spec"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/TagSpec"}, "status": {"$ref": "#/components/schemas/TagStatus"}}}, "TagSpec": {"required": ["displayName", "slug"], "type": "object", "properties": {"color": {"pattern": "^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$", "type": "string", "description": "Color regex explanation.\n <pre>\n ^                 # start of the line\n #                 # start with a number sign `#`\n (                 # start of (group 1)\n   [a-fA-F0-9]{6}  # support z-f, A-F and 0-9, with a length of 6\n   |               # or\n   [a-fA-F0-9]{3}  # support z-f, A-F and 0-9, with a length of 3\n )                 # end of (group 1)\n $                 # end of the line\n </pre>"}, "cover": {"type": "string"}, "displayName": {"minLength": 1, "type": "string"}, "slug": {"minLength": 1, "type": "string"}}}, "TagStatus": {"type": "object", "properties": {"observedVersion": {"type": "integer", "format": "int64"}, "permalink": {"type": "string"}, "postCount": {"type": "integer", "format": "int32"}, "visiblePostCount": {"type": "integer", "format": "int32"}}}, "TestOperation": {"required": ["op", "path", "value"], "type": "object", "properties": {"op": {"type": "string", "enum": ["test"]}, "path": {"pattern": "^(/[^/~]*(~[01][^/~]*)*)*$", "type": "string", "description": "A JSON Pointer path pointing to the location to move/copy from.", "example": "/a/b/c"}, "value": {"description": "Value can be any JSON value"}}}, "TotpAuthLinkResponse": {"type": "object", "properties": {"authLink": {"type": "string", "description": "QR Code with base64 encoded.", "format": "uri"}, "rawSecret": {"type": "string"}}}, "TotpRequest": {"required": ["code", "password", "secret"], "type": "object", "properties": {"code": {"type": "string"}, "password": {"minLength": 1, "type": "string"}, "secret": {"minLength": 1, "type": "string"}}}, "TwoFactorAuthSettings": {"type": "object", "properties": {"available": {"type": "boolean", "description": "Check if 2FA is available."}, "emailVerified": {"type": "boolean"}, "enabled": {"type": "boolean"}, "totpConfigured": {"type": "boolean"}}}, "UcUploadFromUrlRequest": {"required": ["url"], "type": "object", "properties": {"filename": {"type": "string", "description": "Custom file name"}, "url": {"type": "string", "format": "url"}}}, "UcUploadRequest": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "formData": {"type": "object", "properties": {"all": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Part"}, "writeOnly": true}, "empty": {"type": "boolean"}}, "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Part"}}}}}, "UserConnection": {"required": ["apiVersion", "kind", "metadata", "spec"], "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}, "metadata": {"$ref": "#/components/schemas/Metadata"}, "spec": {"$ref": "#/components/schemas/UserConnectionSpec"}}, "description": "User connection extension."}, "UserConnectionSpec": {"required": ["providerUserId", "registrationId", "username"], "type": "object", "properties": {"providerUserId": {"type": "string", "description": "The unique identifier for the user's connection to the OAuth provider.\n for example, the user's GitHub id."}, "registrationId": {"type": "string", "description": "The name of the OAuth provider (e.g. Google, Facebook, Twitter)."}, "updatedAt": {"type": "string", "description": "The time when the user connection was last updated.", "format": "date-time"}, "username": {"type": "string", "description": "The {@link Metadata#getName Metadata#getName()} of the user associated with the OAuth connection."}}}, "UserDevice": {"required": ["active", "currentDevice", "device"], "type": "object", "properties": {"active": {"type": "boolean"}, "currentDevice": {"type": "boolean"}, "device": {"$ref": "#/components/schemas/Device"}}}}, "securitySchemes": {"basicAuth": {"scheme": "basic", "type": "http"}, "bearerAuth": {"bearerFormat": "JWT", "scheme": "bearer", "type": "http"}}}}