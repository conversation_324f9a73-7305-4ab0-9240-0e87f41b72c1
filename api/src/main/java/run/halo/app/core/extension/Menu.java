package run.halo.app.core.extension;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.LinkedHashSet;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import run.halo.app.extension.AbstractExtension;
import run.halo.app.extension.GVK;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@GVK(group = "", version = "v1alpha1", kind = "Menu", plural = "menus", singular = "menu")
public class Menu extends AbstractExtension {

    @Schema(description = "The spec of menu.", requiredMode = REQUIRED)
    private Spec spec;

    @Data
    @Schema(name = "MenuSpec")
    public static class Spec {

        @Schema(description = "The display name of the menu.", requiredMode = REQUIRED)
        private String displayName;

        @ArraySchema(
            uniqueItems = true,
            arraySchema = @Schema(description = "Menu items of this menu."),
            schema = @Schema(description = "Name of menu item.")
        )
        private LinkedHashSet<String> menuItems;

    }

}
