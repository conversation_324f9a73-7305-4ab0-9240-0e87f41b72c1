package run.halo.app.theme.dialect;

import org.pf4j.ExtensionPoint;
import org.thymeleaf.context.ITemplateContext;
import org.thymeleaf.model.IProcessableElementTag;
import org.thymeleaf.processor.element.IElementTagStructureHandler;

/**
 * Comment widget extension point to extend the &#x3C;halo:comment /&#x3E; tag of the theme-side.
 *
 * <AUTHOR>
 * @since 2.0.0
 */
public interface CommentWidget extends ExtensionPoint {

    String ENABLE_COMMENT_ATTRIBUTE = CommentWidget.class.getName() + ".ENABLE";

    void render(ITemplateContext context, IProcessableElementTag tag,
        IElementTagStructureHandler structureHandler);
}
