problemDetail.title.org.springframework.web.server.ServerWebInputException=请求参数有误
problemDetail.title.org.springframework.security.authentication.BadCredentialsException=无效凭据
problemDetail.title.run.halo.app.infra.exception.UnsatisfiedAttributeValueException=请求参数属性值不满足要求
problemDetail.title.run.halo.app.infra.exception.PluginInstallationException=插件安装失败
problemDetail.title.run.halo.app.infra.exception.AttachmentAlreadyExistsException=附件已存在
problemDetail.title.run.halo.app.infra.exception.FileTypeNotAllowedException=文件类型不允许
problemDetail.title.run.halo.app.infra.exception.FileSizeExceededException=文件大小超出限制
problemDetail.title.run.halo.app.infra.exception.RequestRestrictedException=请求受限
problemDetail.title.run.halo.app.infra.exception.DuplicateNameException=名称重复
problemDetail.title.run.halo.app.infra.exception.PluginAlreadyExistsException=插件已存在
problemDetail.title.run.halo.app.infra.exception.ThemeInstallationException=主题安装失败
problemDetail.title.run.halo.app.infra.exception.ThemeAlreadyExistsException=主题已存在
problemDetail.title.run.halo.app.infra.exception.RateLimitExceededException=请求限制
problemDetail.title.run.halo.app.infra.exception.NotFoundException=资源不存在
problemDetail.title.run.halo.app.infra.exception.EmailVerificationFailed=邮箱验证失败
problemDetail.title.run.halo.app.infra.exception.PluginDependencyException$CyclicException=循环依赖
problemDetail.title.run.halo.app.infra.exception.PluginDependencyException$NotFoundException=依赖未找到
problemDetail.title.run.halo.app.infra.exception.PluginDependencyException$WrongVersionsException=依赖版本错误
problemDetail.title.run.halo.app.infra.exception.PluginDependentsNotDisabledException=子插件未禁用
problemDetail.title.run.halo.app.infra.exception.PluginDependenciesNotEnabledException=依赖未启用
problemDetail.title.run.halo.app.infra.exception.OAuth2UserAlreadyBoundException=用户已绑定错误
problemDetail.title.run.halo.app.infra.exception.PluginRuntimeIncompatibleException=插件运行时不兼容

problemDetail.title.internalServerError=服务器内部错误
problemDetail.title.conflict=冲突

problemDetail.org.springframework.security.authentication.BadCredentialsException=用户名或密码错误。
problemDetail.run.halo.app.infra.exception.AttachmentAlreadyExistsException=文件 {0} 已存在，建议更名后重试。
problemDetail.run.halo.app.infra.exception.DuplicateNameException=检测到有重复的名称，请重命名后重试。
problemDetail.run.halo.app.infra.exception.PluginAlreadyExistsException=插件 {0} 已经存在。
problemDetail.run.halo.app.infra.exception.RateLimitExceededException=请求过于频繁，请稍候再试。
problemDetail.run.halo.app.infra.exception.EmailVerificationFailed=验证码错误或已失效。
problemDetail.run.halo.app.infra.exception.PluginDependencyException$CyclicException=检测到循环依赖。
problemDetail.run.halo.app.infra.exception.PluginDependencyException$NotFoundException=依赖“{0}”未找到。
problemDetail.run.halo.app.infra.exception.PluginDependencyException$WrongVersionsException=依赖版本有误：{0}。
problemDetail.run.halo.app.infra.exception.PluginDependentsNotDisabledException=子插件 {0} 未完全禁用，请先禁用它们。
problemDetail.run.halo.app.infra.exception.PluginDependenciesNotEnabledException=插件依赖 {0} 未完全启用，请先启用它们。
problemDetail.run.halo.app.infra.exception.OAuth2UserAlreadyBoundException=用户 {0} 已经绑定到另一个 OAuth2 用户，无法自动绑定当前 OAuth2 用户。
problemDetail.run.halo.app.infra.exception.PluginRuntimeIncompatibleException=插件和当前 Halo 运行时不兼容，请升级插件或降级 Halo 运行时。

problemDetail.index.duplicateKey=唯一索引 {1} 中的值 {0} 已存在，请更名后重试。
problemDetail.user.email.verify.maxAttempts=尝试次数过多，请稍候再试。
problemDetail.user.email.verify.emailInUse=邮箱已被使用, 请更换邮箱后重试。
problemDetail.user.password.unsatisfied=密码不符合规范。
problemDetail.user.username.unsatisfied=用户名不符合规范。
problemDetail.user.oldPassword.notMatch=旧密码不匹配。
problemDetail.user.password.notMatch=密码不匹配。
problemDetail.user.signUpFailed.disallowed=系统不允许注册新用户。
problemDetail.user.duplicateName=用户名 {0} 已存在，请更换用户名后重试。
problemDetail.plugin.version.unsatisfied.requires=插件要求一个最小的系统版本为 {0}, 但当前版本为 {1}。
problemDetail.plugin.missingManifest=缺少 plugin.yaml 配置文件或配置文件不符合规范。
problemDetail.theme.version.unsatisfied.requires=主题要求一个最小的系统版本为 {0}, 但当前版本为 {1}。
problemDetail.theme.install.missingManifest=缺少 theme.yaml 配置文件或配置文件不符合规范。
problemDetail.theme.install.alreadyExists=主题 {0} 已存在。
problemDetail.internalServerError=服务器内部发生错误，请稍候再试。
problemDetail.conflict=检测到冲突，请检查数据后重试。
problemDetail.migration.backup.notFound=备份文件不存在或已删除。
problemDetail.attachment.upload.fileSizeExceeded=最大支持上传 {0} 大小的文件。
problemDetail.attachment.upload.fileTypeNotSupported=不支持上传 {0} 类型的文件。
problemDetail.attachment.upload.fileTypeNotMatch=文件类型 {0} 与文件扩展名不匹配，上传被拒绝。
problemDetail.comment.waitingForApproval=评论审核中。

title.visibility.identification.private=（私有）
signup.error.confirm-password-not-match=确认密码与密码不匹配。
signup.error.email-code.invalid=邮箱验证码无效。
signup.error.email.already-taken=邮箱地址已被注册。

validation.error.email.pattern=邮箱格式不正确
validation.error.username.pattern=用户名只能小写且只能包含字母、数字、中划线和点，以字符开头和结尾
validation.error.password.pattern=密码只能使用大小写字母 (A-Z, a-z)、数字 (0-9)，以及以下特殊字符: !@#$%^&*
validation.error.password.size=密码长度必须在 {0} 到 {1} 之间
