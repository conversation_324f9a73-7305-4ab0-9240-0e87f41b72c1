<form
    th:fragment="form"
    class="halo-form"
    name="signup-form"
    id="signup-form"
    th:action="@{/signup}"
    th:object="${form}"
    method="post"
>
    <div class="alert alert-error" role="alert" th:if="${error == 'invalid-email-code'}">
        <strong>
            <span th:text="#{form.error.invalidEmailCode}"></span>
        </strong>
    </div>
    <div class="alert alert-error" role="alert" th:if="${error == 'rate-limit-exceeded'}">
        <strong>
            <span th:text="#{form.error.rateLimitExceeded}"></span>
        </strong>
    </div>
    <div class="alert alert-error" role="alert" th:if="${error == 'duplicate-username'}">
        <strong>
            <span th:text="#{form.error.duplicateUsername}"></span>
        </strong>
    </div>
    <div class="form-item-group">
        <div class="form-item">
            <label for="username" th:text="#{form.username.label}"></label>
            <div class="form-input">
                <input
                    type="text"
                    id="username"
                    name="username"
                    autocomplete="off"
                    spellcheck="false"
                    autocorrect="off"
                    autocapitalize="off"
                    autofocus
                    required
                    minlength="4"
                    maxlength="63"
                    th:field="*{username}"
                />
            </div>
            <p class="alert alert-error" th:if="${#fields.hasErrors('username')}" th:errors="*{username}"></p>
        </div>

        <div class="form-item">
            <label for="displayName" th:text="#{form.displayName.label}"></label>
            <div class="form-input">
                <input
                    type="text"
                    id="displayName"
                    name="displayName"
                    autocomplete="off"
                    spellcheck="false"
                    autocorrect="off"
                    autocapitalize="off"
                    required
                    th:field="*{displayName}"
                />
            </div>
            <p class="alert alert-error" th:if="${#fields.hasErrors('displayName')}" th:errors="*{displayName}"></p>
        </div>
    </div>

    <div class="form-item-group">
        <div class="form-item">
            <label for="email" th:text="#{form.email.label}"></label>
            <div class="form-input">
                <input
                    type="email"
                    id="email"
                    name="email"
                    autocomplete="off"
                    spellcheck="false"
                    autocorrect="off"
                    autocapitalize="off"
                    required
                    th:field="*{email}"
                />
            </div>
            <p class="alert alert-error" th:if="${#fields.hasErrors('email')}" th:errors="*{email}"></p>
        </div>

        <div class="form-item" th:if="${globalInfo.mustVerifyEmailOnRegistration}">
            <label for="emailCode" th:text="#{form.emailCode.label}"></label>
            <div class="form-input-group">
                <div class="form-input">
                    <input type="text" inputmode="numeric" pattern="\d*" id="emailCode" name="emailCode" required />
                </div>

                <button id="emailCodeSendButton" type="button" th:text="#{form.emailCode.sendButton}"></button>
            </div>
            <p class="alert alert-error" th:if="${#fields.hasErrors('emailCode')}" th:errors="*{emailCode}"></p>
        </div>
    </div>

    <div class="form-item">
        <label for="password" th:text="#{form.password.label}"></label>
        <th:block
            th:replace="~{gateway_fragments/input :: password(id = 'password', name = 'password', required = 'true', minlength = 5, maxlength = 257, enableToggle = true)}"
        ></th:block>
        <p class="alert alert-error" th:if="${#fields.hasErrors('password')}" th:errors="*{password}"></p>
    </div>

    <div class="form-item">
        <label for="confirmPassword" th:text="#{form.confirmPassword.label}"></label>
        <th:block
            th:replace="~{gateway_fragments/input :: password(id = 'confirmPassword', name = 'confirmPassword', required = 'true', minlength = 5, maxlength = 257, enableToggle = true)}"
        ></th:block>
        <p class="alert alert-error" th:if="${#fields.hasErrors('confirmPassword')}" th:errors="*{confirmPassword}"></p>
    </div>

    <div class="form-item">
        <button type="submit" th:text="#{form.submit}"></button>
    </div>

    <script th:inline="javascript">
        document.addEventListener("DOMContentLoaded", function () {
            setupPasswordConfirmation("password", "confirmPassword");
        });
    </script>

    <script th:if="${globalInfo.mustVerifyEmailOnRegistration}" th:inline="javascript">
        document.addEventListener("DOMContentLoaded", function () {
            const headerName = /*[[${_csrf.headerName}]]*/ "";
            const token = /*[[${_csrf.token}]]*/ "";
            async function sendRequest() {
                const email = document.getElementById("email").value;

                if (!email) {
                    throw new Error(/*[[#{form.emailCode.send.emptyValidation}]]*/ "");
                }

                const response = await fetch("/signup/send-email-code", {
                    method: "POST",
                    body: JSON.stringify({ email: email }),
                    headers: {
                        "Content-Type": "application/json",
                        [headerName]: token,
                    },
                });

                if (!response.ok) {
                    const json = await response.json();
                    if (json.errors && json.errors.length) {
                        throw new Error(json.errors[0]);
                    }
                }

                return response;
            }

            const emailCodeSendButton = document.getElementById("emailCodeSendButton");
            sendVerificationCode(emailCodeSendButton, sendRequest);
        });
    </script>
</form>
