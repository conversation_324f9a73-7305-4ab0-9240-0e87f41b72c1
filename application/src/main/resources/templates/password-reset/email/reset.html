<!doctype html>
<html
    xmlns:th="https://www.thymeleaf.org"
    th:replace="~{gateway_fragments/layout:: layout(title = |#{title(${username})} - ${site.title}|, head = null, body = ~{::body})}"
>
    <th:block th:fragment="body">
        <div class="gateway-wrapper">
            <div th:replace="~{gateway_fragments/common::haloLogo}"></div>
            <div class="halo-form-wrapper">
                <h1 class="form-title" th:text="#{title(${username})}"></h1>
                <form th:replace="~{gateway_fragments/password_reset_email_reset::form}"></form>
            </div>
            <div th:replace="~{gateway_fragments/common::languageSwitcher}"></div>
        </div>
    </th:block>
</html>
