package run.halo.app.notification;

import static org.assertj.core.api.Assertions.assertThat;

import org.junit.jupiter.api.Test;

/**
 * Tests for {@link UserIdentity}.
 *
 * <AUTHOR>
 * @since 2.9.0
 */
class UserIdentityTest {


    @Test
    void getEmailTest() {
        var identity = UserIdentity.anonymousWithEmail("<EMAIL>");
        assertThat(identity.getEmail().orElse(null)).isEqualTo("<EMAIL>");
    }
}