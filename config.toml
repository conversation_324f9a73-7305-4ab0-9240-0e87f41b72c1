listen_addr = "127.0.0.1:8008"

[db]
url = "mysql://root:root@localhost/halo-web"

[jwt]
secret = "yoursecret"
expiry = 3600

[log]
file_name = "app.log"
rolling = "daily"

[security]
[security.session]
secret = "your-session-secret-key-change-in-production-64-chars-long"
name = "HALO_SESSION"
max_age = 604800  # 7 days
secure = false    # Set to true in production with HTTPS
http_only = true
same_site = "Lax"

[security.csrf]
enabled = true
secret = "your-csrf-secret-key-change-in-production-32-chars"
token_name = "csrf_token"
header_name = "X-CSRF-TOKEN"

[security.cors]
enabled = true
allowed_origins = ["*"]
allowed_methods = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
allowed_headers = ["Authorization", "Content-Type", "Accept", "X-CSRF-TOKEN", "<PERSON>ie"]
allow_credentials = true
max_age = 3600

[security.remember_me]
enabled = true
secret = "your-remember-me-secret-key-change-in-production"
token_validity = 1209600  # 14 days
cookie_name = "remember-me"

[security.anonymous]
enabled = true
principal = "anonymous"
role = "ROLE_ANONYMOUS"

[security.headers]
frame_options_enabled = true
frame_options = "SAMEORIGIN"
content_type_options = true
xss_protection = true
referrer_policy = "strict-origin-when-cross-origin"
