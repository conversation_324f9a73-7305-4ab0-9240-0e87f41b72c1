use serde::Deserialize;
use time::Duration;

#[derive(Deserialize, Clone, Debug)]
pub struct SecurityConfig {
    pub session: SessionConfig,
    pub csrf: CsrfConfig,
    pub cors: CorsConfig,
    pub remember_me: RememberMeConfig,
    pub anonymous: AnonymousConfig,
    pub headers: SecurityHeadersConfig,
}

#[derive(Deserialize, Clone, Debug)]
pub struct SessionConfig {
    #[serde(default = "default_session_secret")]
    pub secret: String,
    #[serde(default = "default_session_name")]
    pub name: String,
    #[serde(default = "default_session_max_age")]
    pub max_age: i64, // seconds
    #[serde(default = "default_true")]
    pub secure: bool,
    #[serde(default = "default_true")]
    pub http_only: bool,
    #[serde(default = "default_session_same_site")]
    pub same_site: String,
}

#[derive(Deserialize, Clone, Debug)]
pub struct CsrfConfig {
    #[serde(default = "default_true")]
    pub enabled: bool,
    #[serde(default = "default_csrf_secret")]
    pub secret: String,
    #[serde(default = "default_csrf_token_name")]
    pub token_name: String,
    #[serde(default = "default_csrf_header_name")]
    pub header_name: String,
}

#[derive(Deserialize, Clone, Debug)]
pub struct CorsConfig {
    #[serde(default = "default_true")]
    pub enabled: bool,
    #[serde(default = "default_cors_origins")]
    pub allowed_origins: Vec<String>,
    #[serde(default = "default_cors_methods")]
    pub allowed_methods: Vec<String>,
    #[serde(default = "default_cors_headers")]
    pub allowed_headers: Vec<String>,
    #[serde(default = "default_true")]
    pub allow_credentials: bool,
    #[serde(default = "default_cors_max_age")]
    pub max_age: i64,
}

#[derive(Deserialize, Clone, Debug)]
pub struct RememberMeConfig {
    #[serde(default = "default_true")]
    pub enabled: bool,
    #[serde(default = "default_remember_me_secret")]
    pub secret: String,
    #[serde(default = "default_remember_me_token_validity")]
    pub token_validity: i64, // seconds (14 days default)
    #[serde(default = "default_remember_me_cookie_name")]
    pub cookie_name: String,
}

#[derive(Deserialize, Clone, Debug)]
pub struct AnonymousConfig {
    #[serde(default = "default_true")]
    pub enabled: bool,
    #[serde(default = "default_anonymous_principal")]
    pub principal: String,
    #[serde(default = "default_anonymous_role")]
    pub role: String,
}

#[derive(Deserialize, Clone, Debug)]
pub struct SecurityHeadersConfig {
    #[serde(default = "default_true")]
    pub frame_options_enabled: bool,
    #[serde(default = "default_frame_options")]
    pub frame_options: String,
    #[serde(default = "default_true")]
    pub content_type_options: bool,
    #[serde(default = "default_true")]
    pub xss_protection: bool,
    #[serde(default = "default_referrer_policy")]
    pub referrer_policy: String,
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            session: SessionConfig::default(),
            csrf: CsrfConfig::default(),
            cors: CorsConfig::default(),
            remember_me: RememberMeConfig::default(),
            anonymous: AnonymousConfig::default(),
            headers: SecurityHeadersConfig::default(),
        }
    }
}

impl Default for SessionConfig {
    fn default() -> Self {
        Self {
            secret: default_session_secret(),
            name: default_session_name(),
            max_age: default_session_max_age(),
            secure: default_true(),
            http_only: default_true(),
            same_site: default_session_same_site(),
        }
    }
}

impl Default for CsrfConfig {
    fn default() -> Self {
        Self {
            enabled: default_true(),
            secret: default_csrf_secret(),
            token_name: default_csrf_token_name(),
            header_name: default_csrf_header_name(),
        }
    }
}

impl Default for CorsConfig {
    fn default() -> Self {
        Self {
            enabled: default_true(),
            allowed_origins: default_cors_origins(),
            allowed_methods: default_cors_methods(),
            allowed_headers: default_cors_headers(),
            allow_credentials: default_true(),
            max_age: default_cors_max_age(),
        }
    }
}

impl Default for RememberMeConfig {
    fn default() -> Self {
        Self {
            enabled: default_true(),
            secret: default_remember_me_secret(),
            token_validity: default_remember_me_token_validity(),
            cookie_name: default_remember_me_cookie_name(),
        }
    }
}

impl Default for AnonymousConfig {
    fn default() -> Self {
        Self {
            enabled: default_true(),
            principal: default_anonymous_principal(),
            role: default_anonymous_role(),
        }
    }
}

impl Default for SecurityHeadersConfig {
    fn default() -> Self {
        Self {
            frame_options_enabled: default_true(),
            frame_options: default_frame_options(),
            content_type_options: default_true(),
            xss_protection: default_true(),
            referrer_policy: default_referrer_policy(),
        }
    }
}

// Default value functions
fn default_true() -> bool {
    true
}

fn default_session_secret() -> String {
    "your-session-secret-key-change-in-production".to_string()
}

fn default_session_name() -> String {
    "HALO_SESSION".to_string()
}

fn default_session_max_age() -> i64 {
    3600 * 24 * 7 // 7 days
}

fn default_session_same_site() -> String {
    "Lax".to_string()
}

fn default_csrf_secret() -> String {
    "your-csrf-secret-key-change-in-production".to_string()
}

fn default_csrf_token_name() -> String {
    "csrf_token".to_string()
}

fn default_csrf_header_name() -> String {
    "X-CSRF-TOKEN".to_string()
}

fn default_cors_origins() -> Vec<String> {
    vec!["*".to_string()]
}

fn default_cors_methods() -> Vec<String> {
    vec![
        "GET".to_string(),
        "POST".to_string(),
        "PUT".to_string(),
        "DELETE".to_string(),
        "PATCH".to_string(),
        "OPTIONS".to_string(),
    ]
}

fn default_cors_headers() -> Vec<String> {
    vec![
        "Authorization".to_string(),
        "Content-Type".to_string(),
        "Accept".to_string(),
        "X-CSRF-TOKEN".to_string(),
        "Cookie".to_string(),
    ]
}

fn default_cors_max_age() -> i64 {
    3600
}

fn default_remember_me_secret() -> String {
    "your-remember-me-secret-key-change-in-production".to_string()
}

fn default_remember_me_token_validity() -> i64 {
    3600 * 24 * 14 // 14 days
}

fn default_remember_me_cookie_name() -> String {
    "remember-me".to_string()
}

fn default_anonymous_principal() -> String {
    "anonymous".to_string()
}

fn default_anonymous_role() -> String {
    "ROLE_ANONYMOUS".to_string()
}

fn default_frame_options() -> String {
    "SAMEORIGIN".to_string()
}

fn default_referrer_policy() -> String {
    "strict-origin-when-cross-origin".to_string()
}
