use anyhow::Result;
use jsonwebtoken::{decode, Al<PERSON><PERSON>m, <PERSON><PERSON><PERSON><PERSON><PERSON>, Encoding<PERSON>ey, Valida<PERSON>};
use salvo::jwt_auth::{ConstDecoder, <PERSON>ie<PERSON>inder, HeaderFinder, QueryFinder};
use salvo::prelude::*;
use serde::{Deserialize, Serialize};
use time::{Duration, OffsetDateTime};

use crate::config::{self, JwtConfig};

#[derive(Debug, Serialize, Deserialize)]
pub struct JwtClaims {
    pub uid: String,
    pub exp: i64,
}

pub fn auth_hoop(config: &JwtConfig) -> JwtAuth<JwtClaims, ConstDecoder> {
    JwtAuth::new(ConstDecoder::from_secret(
        config.secret.to_owned().as_bytes(),
    ))
    .finders(vec![
        Box::new(HeaderFinder::new()),
        Box::new(QueryFinder::new("token")),
        Box::new(<PERSON><PERSON><PERSON>inder::new("jwt_token")),
    ])
    .force_passed(false)
}

pub fn get_token(uid: impl Into<String>) -> Result<(String, i64)> {
    let exp = OffsetDateTime::now_utc() + Duration::seconds(config::get().jwt.expiry);
    let claim = JwtClaims {
        uid: uid.into(),
        exp: exp.unix_timestamp(),
    };
    let token: String = jsonwebtoken::encode(
        &jsonwebtoken::Header::default(),
        &claim,
        &EncodingKey::from_secret(config::get().jwt.secret.as_bytes()),
    )?;
    Ok((token, exp.unix_timestamp()))
}

#[allow(dead_code)]
pub fn decode_token(token: &str) -> bool {
    let validation = Validation::new(Algorithm::HS256);
    decode::<JwtClaims>(
        token,
        &DecodingKey::from_secret(config::get().jwt.secret.as_bytes()),
        &validation,
    )
    .is_ok()
}
