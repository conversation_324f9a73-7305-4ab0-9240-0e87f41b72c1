use salvo::prelude::*;
use crate::config::SecurityConfig;
use crate::hoops::security::auth::{AuthUser, AuthDepotExt};

/// Anonymous user middleware that sets anonymous user if no authentication is found
pub struct AnonymousUserMiddleware {
    config: SecurityConfig,
}

impl AnonymousUserMiddleware {
    pub fn new(config: SecurityConfig) -> Self {
        Self { config }
    }
}

#[async_trait]
impl Handler for AnonymousUserMiddleware {
    async fn handle(
        &self,
        _req: &mut Request,
        depot: &mut Depot,
        _res: &mut Response,
        _ctrl: &mut FlowCtrl,
    ) {
        if !self.config.anonymous.enabled {
            return;
        }

        // Only set anonymous user if no user is already set
        if depot.current_user().is_none() {
            let anonymous_user = AuthUser {
                id: self.config.anonymous.principal.clone(),
                username: self.config.anonymous.principal.clone(),
                roles: vec![self.config.anonymous.role.clone()],
                is_anonymous: true,
            };
            depot.set_current_user(anonymous_user);
        }
    }
}

/// Middleware that allows only unauthenticated users (for login/signup pages)
pub struct AnonymousOnlyMiddleware {
    pub redirect_to: String,
}

impl AnonymousOnlyMiddleware {
    pub fn new(redirect_to: String) -> Self {
        Self { redirect_to }
    }
}

#[async_trait]
impl Handler for AnonymousOnlyMiddleware {
    async fn handle(
        &self,
        _req: &mut Request,
        depot: &mut Depot,
        res: &mut Response,
        ctrl: &mut FlowCtrl,
    ) {
        if depot.is_authenticated() {
            res.render(Redirect::other(&self.redirect_to));
            ctrl.skip_rest();
        }
    }
}

/// Middleware that allows both authenticated and anonymous users
pub struct AnonymousOrAuthenticatedMiddleware;

#[async_trait]
impl Handler for AnonymousOrAuthenticatedMiddleware {
    async fn handle(
        &self,
        _req: &mut Request,
        depot: &mut Depot,
        res: &mut Response,
        ctrl: &mut FlowCtrl,
    ) {
        // Check if user exists (either authenticated or anonymous)
        if depot.current_user().is_none() {
            res.render(StatusError::unauthorized());
            ctrl.skip_rest();
        }
    }
}
