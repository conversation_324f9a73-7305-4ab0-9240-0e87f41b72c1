use salvo::prelude::*;
use salvo::session::SessionDepotExt;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use sea_orm::{EntityTrait};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthUser {
    pub id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub is_anonymous: bool,
}

impl AuthUser {
    pub fn new(id: String, username: String, roles: Vec<String>) -> Self {
        Self {
            id,
            username,
            roles,
            is_anonymous: false,
        }
    }

    pub fn anonymous() -> Self {
        Self {
            id: "anonymous".to_string(),
            username: "anonymous".to_string(),
            roles: vec!["ROLE_ANONYMOUS".to_string()],
            is_anonymous: true,
        }
    }

    pub fn has_role(&self, role: &str) -> bool {
        self.roles.contains(&role.to_string())
    }

    pub fn has_any_role(&self, roles: &[&str]) -> bool {
        let role_set: HashSet<String> = roles.iter().map(|r| r.to_string()).collect();
        self.roles.iter().any(|r| role_set.contains(r))
    }

    pub fn is_authenticated(&self) -> bool {
        !self.is_anonymous
    }
}

pub trait AuthDepotExt {
    fn current_user(&self) -> Option<&AuthUser>;
    fn set_current_user(&mut self, user: AuthUser);
    fn clear_current_user(&mut self);
    fn is_authenticated(&self) -> bool;
    fn has_role(&self, role: &str) -> bool;
    fn has_any_role(&self, roles: &[&str]) -> bool;
}

impl AuthDepotExt for Depot {
    fn current_user(&self) -> Option<&AuthUser> {
        self.get::<AuthUser>("current_user").ok()
    }

    fn set_current_user(&mut self, user: AuthUser) {
        self.insert("current_user", user);
    }

    fn clear_current_user(&mut self) {
        self.remove::<AuthUser>("current_user");
    }

    fn is_authenticated(&self) -> bool {
        self.current_user()
            .map(|u| u.is_authenticated())
            .unwrap_or(false)
    }

    fn has_role(&self, role: &str) -> bool {
        self.current_user()
            .map(|u| u.has_role(role))
            .unwrap_or(false)
    }

    fn has_any_role(&self, roles: &[&str]) -> bool {
        self.current_user()
            .map(|u| u.has_any_role(roles))
            .unwrap_or(false)
    }
}

/// Authentication middleware that loads user from session or JWT
pub struct AuthMiddleware;

#[async_trait]
impl Handler for AuthMiddleware {
    async fn handle(
        &self,
        req: &mut Request,
        depot: &mut Depot,
        res: &mut Response,
        ctrl: &mut FlowCtrl,
    ) {
        // Try to load user from session first
        if let Some(session) = depot.session() {
            if let Some(user_id) = session.get::<String>("user_id") {
                if let Some(username) = session.get::<String>("username") {
                    if let Some(roles) = session.get::<Vec<String>>("roles") {
                        let user = AuthUser::new(user_id, username, roles);
                        depot.set_current_user(user);
                        return;
                    }
                }
            }
        }

        // Try to load user from JWT token
        if let Some(jwt_data) = depot.jwt_auth_data::<crate::hoops::jwt::JwtClaims>() {
            // Load user details from database using JWT uid
            // Note: This is a simplified version - in a real implementation you'd load from DB
            let user = AuthUser::new(
                jwt_data.claims.uid.clone(),
                jwt_data.claims.uid.clone(), // Use uid as username for now
                vec!["ROLE_USER".to_string()], // Default role, should be loaded from DB
            );
            depot.set_current_user(user);
            return;
        }

        // Set anonymous user if no authentication found
        depot.set_current_user(AuthUser::anonymous());
    }
}

/// Middleware that requires authentication
pub struct RequireAuthMiddleware {
    pub redirect_to: Option<String>,
}

impl RequireAuthMiddleware {
    pub fn new() -> Self {
        Self {
            redirect_to: Some("/login".to_string()),
        }
    }

    pub fn with_redirect(redirect_to: String) -> Self {
        Self {
            redirect_to: Some(redirect_to),
        }
    }

    pub fn no_redirect() -> Self {
        Self {
            redirect_to: None,
        }
    }
}

#[async_trait]
impl Handler for RequireAuthMiddleware {
    async fn handle(
        &self,
        req: &mut Request,
        depot: &mut Depot,
        res: &mut Response,
        ctrl: &mut FlowCtrl,
    ) {
        if !depot.is_authenticated() {
            if let Some(redirect_to) = &self.redirect_to {
                let current_url = req.uri().to_string();
                let redirect_url = if current_url != "/" && !current_url.is_empty() {
                    format!("{}?redirect_uri={}", redirect_to, urlencoding::encode(&current_url))
                } else {
                    redirect_to.clone()
                };
                res.render(Redirect::other(redirect_url));
            } else {
                res.render(StatusError::unauthorized());
            }
            ctrl.skip_rest();
        }
    }
}

/// Middleware that requires specific roles
pub struct RequireRoleMiddleware {
    pub roles: Vec<String>,
    pub require_all: bool,
}

impl RequireRoleMiddleware {
    pub fn new(roles: Vec<&str>) -> Self {
        Self {
            roles: roles.iter().map(|r| r.to_string()).collect(),
            require_all: false,
        }
    }

    pub fn require_all(roles: Vec<&str>) -> Self {
        Self {
            roles: roles.iter().map(|r| r.to_string()).collect(),
            require_all: true,
        }
    }
}

#[async_trait]
impl Handler for RequireRoleMiddleware {
    async fn handle(
        &self,
        _req: &mut Request,
        depot: &mut Depot,
        res: &mut Response,
        ctrl: &mut FlowCtrl,
    ) {
        if !depot.is_authenticated() {
            res.render(StatusError::unauthorized());
            ctrl.skip_rest();
            return;
        }

        let has_permission = if self.require_all {
            self.roles.iter().all(|role| depot.has_role(role))
        } else {
            let role_refs: Vec<&str> = self.roles.iter().map(|s| s.as_str()).collect();
            depot.has_any_role(&role_refs)
        };

        if !has_permission {
            res.render(StatusError::forbidden());
            ctrl.skip_rest();
        }
    }
}
