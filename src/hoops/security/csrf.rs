use salvo::prelude::*;
use salvo::http::Method;
use crate::config::SecurityConfig;

// Simplified CSRF implementation for now
// In a real implementation, you would use Salvo's CSRF middleware
pub fn create_csrf_handler(security_config: &SecurityConfig) -> Option<()> {
    if !security_config.csrf.enabled {
        return None;
    }

    // Placeholder for CSRF handler
    Some(())
}

/// CSRF protection middleware that can be selectively applied
pub struct CsrfProtection {
    pub skip_paths: Vec<String>,
    pub skip_methods: Vec<Method>,
}

impl CsrfProtection {
    pub fn new() -> Self {
        Self {
            skip_paths: vec![
                "/api/".to_string(),
                "/apis/".to_string(),
                "/actuator/".to_string(),
                "/system/setup".to_string(),
            ],
            skip_methods: vec![Method::GET, Method::HEAD, Method::OPTIONS],
        }
    }

    pub fn skip_path(mut self, path: &str) -> Self {
        self.skip_paths.push(path.to_string());
        self
    }

    pub fn skip_method(mut self, method: Method) -> Self {
        self.skip_methods.push(method);
        self
    }

    fn should_skip(&self, req: &Request) -> bool {
        // Skip certain HTTP methods
        if self.skip_methods.contains(req.method()) {
            return true;
        }

        // Skip certain paths
        let path = req.uri().path();
        for skip_path in &self.skip_paths {
            if path.starts_with(skip_path) {
                return true;
            }
        }

        false
    }
}

#[async_trait]
impl Handler for CsrfProtection {
    async fn handle(
        &self,
        req: &mut Request,
        depot: &mut Depot,
        res: &mut Response,
        ctrl: &mut FlowCtrl,
    ) {
        if self.should_skip(req) {
            return;
        }

        // Check if CSRF token is present and valid
        // This is a simplified version - in practice, you'd integrate with Salvo's CSRF middleware
        let token_from_header = req.headers().get("X-CSRF-TOKEN")
            .and_then(|v| v.to_str().ok());
        
        let token_from_form: Option<&str> = if req.method() == Method::POST {
            // Try to get token from form data
            // This is simplified - you'd need to parse the form properly
            None // Placeholder
        } else {
            None
        };

        if token_from_header.is_none() && token_from_form.is_none() {
            tracing::warn!("CSRF token missing for request: {} {}", req.method(), req.uri().path());
            res.render(StatusError::forbidden().brief("CSRF token missing"));
            ctrl.skip_rest();
        }
    }
}

/// Helper trait to add CSRF token to templates
pub trait CsrfDepotExt {
    fn csrf_token(&self) -> Option<String>;
}

impl CsrfDepotExt for Depot {
    fn csrf_token(&self) -> Option<String> {
        // Get CSRF token from depot (set by Salvo's CSRF middleware)
        self.get::<String>("csrf_token").ok().cloned()
    }
}
