use salvo::prelude::*;
use crate::config::SecurityConfig;

/// Security headers middleware
pub struct SecurityHeadersMiddleware {
    config: SecurityConfig,
}

impl SecurityHeadersMiddleware {
    pub fn new(config: SecurityConfig) -> Self {
        Self { config }
    }
}

#[async_trait]
impl Handler for SecurityHeadersMiddleware {
    async fn handle(
        &self,
        _req: &mut Request,
        _depot: &mut Depot,
        res: &mut Response,
        _ctrl: &mut FlowCtrl,
    ) {
        let headers_config = &self.config.headers;

        // X-Frame-Options
        if headers_config.frame_options_enabled {
            res.headers_mut().insert(
                "X-Frame-Options",
                headers_config.frame_options.parse().unwrap(),
            );
        }

        // X-Content-Type-Options
        if headers_config.content_type_options {
            res.headers_mut().insert(
                "X-Content-Type-Options",
                "nosniff".parse().unwrap(),
            );
        }

        // X-XSS-Protection
        if headers_config.xss_protection {
            res.headers_mut().insert(
                "X-XSS-Protection",
                "1; mode=block".parse().unwrap(),
            );
        }

        // Referrer-Policy
        res.headers_mut().insert(
            "Referrer-Policy",
            headers_config.referrer_policy.parse().unwrap(),
        );

        // Strict-Transport-Security (HSTS)
        // Only add if using HTTPS
        if res.headers().get("content-type").is_some() {
            res.headers_mut().insert(
                "Strict-Transport-Security",
                "max-age=31536000; includeSubDomains".parse().unwrap(),
            );
        }

        // Content-Security-Policy (basic policy)
        res.headers_mut().insert(
            "Content-Security-Policy",
            "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'self'".parse().unwrap(),
        );

        // X-Permitted-Cross-Domain-Policies
        res.headers_mut().insert(
            "X-Permitted-Cross-Domain-Policies",
            "none".parse().unwrap(),
        );
    }
}
