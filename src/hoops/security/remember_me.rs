use salvo::prelude::*;
use cookie::<PERSON><PERSON>;
use serde::{Deserialize, Serialize};
use time::{Duration, OffsetDateTime};
use uuid::Uuid;
use base64;
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
use crate::config::SecurityConfig;
use crate::hoops::security::auth::{AuthUser, AuthDepotExt};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RememberMeToken {
    pub series: String,
    pub token: String,
    pub username: String,
    pub last_used: i64,
}

impl RememberMeToken {
    pub fn new(username: String) -> Self {
        Self {
            series: Uuid::new_v4().to_string(),
            token: Uuid::new_v4().to_string(),
            username,
            last_used: OffsetDateTime::now_utc().unix_timestamp(),
        }
    }

    pub fn refresh_token(&mut self) {
        self.token = Uuid::new_v4().to_string();
        self.last_used = OffsetDateTime::now_utc().unix_timestamp();
    }

    pub fn is_expired(&self, validity_seconds: i64) -> bool {
        let now = OffsetDateTime::now_utc().unix_timestamp();
        (now - self.last_used) > validity_seconds
    }

    pub fn encode(&self, secret: &str) -> String {
        let data = format!("{}:{}:{}:{}", self.series, self.token, self.username, self.last_used);
        let signature = self.sign(&data, secret);
        base64::encode(format!("{}:{}", data, signature))
    }

    pub fn decode(encoded: &str, secret: &str) -> Option<Self> {
        let decoded = base64::decode(encoded).ok()?;
        let decoded_str = String::from_utf8(decoded).ok()?;
        let parts: Vec<&str> = decoded_str.split(':').collect();
        
        if parts.len() != 5 {
            return None;
        }

        let data = format!("{}:{}:{}:{}", parts[0], parts[1], parts[2], parts[3]);
        let signature = parts[4];
        
        let token = Self {
            series: parts[0].to_string(),
            token: parts[1].to_string(),
            username: parts[2].to_string(),
            last_used: parts[3].parse().ok()?,
        };

        if token.sign(&data, secret) == signature {
            Some(token)
        } else {
            None
        }
    }

    fn sign(&self, data: &str, secret: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        data.hash(&mut hasher);
        secret.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}

/// Remember Me middleware
pub struct RememberMeMiddleware;

impl RememberMeMiddleware {
    pub fn new() -> Self {
        Self
    }

    async fn load_user_by_username(&self, username: &str) -> Option<AuthUser> {
        // Load user from database
        // Note: This is a simplified version - in a real implementation you'd load from DB
        Some(AuthUser::new(
            username.to_string(),
            username.to_string(),
            vec!["ROLE_USER".to_string()], // Should load actual roles from DB
        ))
    }

    async fn save_remember_me_token(&self, token: &RememberMeToken) -> Result<(), Box<dyn std::error::Error>> {
        // In a real implementation, save to database
        // For now, we'll just log it
        tracing::info!("Saving remember-me token for user: {}", token.username);
        Ok(())
    }

    async fn load_remember_me_token(&self, series: &str) -> Option<RememberMeToken> {
        // In a real implementation, load from database
        // For now, return None
        tracing::info!("Loading remember-me token for series: {}", series);
        None
    }

    async fn delete_remember_me_token(&self, series: &str) -> Result<(), Box<dyn std::error::Error>> {
        // In a real implementation, delete from database
        tracing::info!("Deleting remember-me token for series: {}", series);
        Ok(())
    }
}

#[async_trait]
impl Handler for RememberMeMiddleware {
    async fn handle(
        &self,
        req: &mut Request,
        depot: &mut Depot,
        res: &mut Response,
        _ctrl: &mut FlowCtrl,
    ) {
        let config = crate::config::get();
        if !config.security.remember_me.enabled {
            return;
        }

        // Skip if user is already authenticated
        if depot.is_authenticated() {
            return;
        }

        // Try to get remember-me cookie
        if let Some(cookie) = req.cookies().get(&config.security.remember_me.cookie_name) {
            let cookie_value = cookie.value();
            
            if let Some(token) = RememberMeToken::decode(cookie_value, &config.security.remember_me.secret) {
                // Check if token is expired
                if token.is_expired(config.security.remember_me.token_validity) {
                    self.delete_remember_me_token(&token.series).await.ok();
                    // Remove expired cookie
                    let expired_cookie = Cookie::build((&config.security.remember_me.cookie_name, ""))
                        .path("/")
                        .max_age(cookie::time::Duration::seconds(-1))
                        .build();
                    res.add_cookie(expired_cookie);
                    return;
                }

                // Load stored token from database
                if let Some(stored_token) = self.load_remember_me_token(&token.series).await {
                    // Verify token matches
                    if stored_token.token == token.token {
                        // Load user
                        if let Some(user) = self.load_user_by_username(&token.username).await {
                            depot.set_current_user(user);
                            
                            // Refresh token for security
                            let mut new_token = stored_token;
                            new_token.refresh_token();
                            
                            // Save new token
                            self.save_remember_me_token(&new_token).await.ok();
                            
                            // Update cookie
                            let new_cookie = Cookie::build((
                                &config.security.remember_me.cookie_name,
                                new_token.encode(&config.security.remember_me.secret)
                            ))
                            .path("/")
                            .max_age(cookie::time::Duration::seconds(config.security.remember_me.token_validity))
                            .http_only(true)
                            .secure(config.security.session.secure)
                            .build();
                            res.add_cookie(new_cookie);
                        }
                    } else {
                        // Token mismatch - possible security breach
                        tracing::warn!("Remember-me token mismatch for user: {}", token.username);
                        self.delete_remember_me_token(&token.series).await.ok();
                    }
                }
            }
        }
    }
}

/// Helper functions for remember me functionality
pub trait RememberMeExt {
    fn set_remember_me_cookie(&mut self, username: &str, config: &SecurityConfig) -> Result<(), Box<dyn std::error::Error>>;
    fn clear_remember_me_cookie(&mut self, config: &SecurityConfig);
}

impl RememberMeExt for Response {
    fn set_remember_me_cookie(&mut self, username: &str, config: &SecurityConfig) -> Result<(), Box<dyn std::error::Error>> {
        if !config.remember_me.enabled {
            return Ok(());
        }

        let token = RememberMeToken::new(username.to_string());
        let cookie_value = token.encode(&config.remember_me.secret);

        let cookie = Cookie::build((config.remember_me.cookie_name.clone(), cookie_value))
            .path("/")
            .max_age(cookie::time::Duration::seconds(config.remember_me.token_validity))
            .http_only(true)
            .secure(config.session.secure)
            .build();

        self.add_cookie(cookie);

        // In a real implementation, save token to database
        tracing::info!("Setting remember-me cookie for user: {}", username);

        Ok(())
    }

    fn clear_remember_me_cookie(&mut self, config: &SecurityConfig) {
        let expired_cookie = Cookie::build((config.remember_me.cookie_name.clone(), "".to_string()))
            .path("/")
            .max_age(cookie::time::Duration::seconds(-1))
            .build();
        self.add_cookie(expired_cookie);
    }
}
