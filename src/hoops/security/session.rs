use salvo::prelude::*;
use salvo::session::{<PERSON><PERSON><PERSON><PERSON>, CookieStore};
use crate::config::SecurityConfig;

pub fn create_session_handler(security_config: &SecurityConfig) -> SessionHandler<CookieStore> {
    let session_config = &security_config.session;

    SessionHandler::builder(
        CookieStore::new(),
        session_config.secret.as_bytes(),
    )
    .cookie_name(&session_config.name)
    .build()
    .expect("Failed to create session handler")
}

/// Session utilities for authentication
pub trait SessionAuthExt {
    fn login_user(&mut self, user_id: &str, username: &str, roles: &[String]);
    fn logout_user(&mut self);
    fn is_logged_in(&self) -> bool;
    fn get_user_id(&self) -> Option<String>;
    fn get_username(&self) -> Option<String>;
    fn get_user_roles(&self) -> Option<Vec<String>>;
}

impl SessionAuthExt for salvo::session::Session {
    fn login_user(&mut self, user_id: &str, username: &str, roles: &[String]) {
        self.insert("user_id", user_id.to_string()).ok();
        self.insert("username", username.to_string()).ok();
        self.insert("roles", roles.clone()).ok();
        self.insert("login_time", time::OffsetDateTime::now_utc().unix_timestamp()).ok();
    }

    fn logout_user(&mut self) {
        self.remove("user_id");
        self.remove("username");
        self.remove("roles");
        self.remove("login_time");
        self.remove("remember_me_token");
    }

    fn is_logged_in(&self) -> bool {
        self.get::<String>("user_id").is_some()
    }

    fn get_user_id(&self) -> Option<String> {
        self.get::<String>("user_id")
    }

    fn get_username(&self) -> Option<String> {
        self.get::<String>("username")
    }

    fn get_user_roles(&self) -> Option<Vec<String>> {
        self.get::<Vec<String>>("roles")
    }
}
