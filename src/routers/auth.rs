use cookie::<PERSON><PERSON>;
use rinja::Template;
use salvo::oapi::extract::*;
use salvo::prelude::*;
use salvo::session::SessionDepotExt;
use sea_orm::{ColumnTrait, EntityTrait, QueryFilter};
use serde::{Deserialize, Serialize};

use crate::entities::users::Model;
use crate::entities::{prelude::Users, users};
use crate::hoops::jwt;
use crate::hoops::security::{AuthDepotExt, AuthUser, RememberMeExt, SessionAuthExt};
use crate::{db, json_ok, utils, AppResult, JsonResult};

#[handler]
pub async fn login_page(res: &mut Response) -> AppResult<()> {
    #[derive(Template)]
    #[template(path = "login.html")]
    struct LoginTemplate {}
    if let Some(cookie) = res.cookies().get("jwt_token") {
        let token = cookie.value().to_string();
        if jwt::decode_token(&token) {
            res.render(Redirect::other("/users"));
            return Ok(());
        }
    }
    let hello_tmpl = LoginTemplate {};
    res.render(Text::Html(hello_tmpl.render().unwrap()));
    Ok(())
}

#[derive(Deserialize, ToSchema, Default, Debug)]
pub struct LoginInData {
    pub username: String,
    pub password: String,
    #[serde(default)]
    pub remember_me: bool,
    pub redirect_uri: Option<String>,
}
#[derive(Serialize, ToSchema, Default, Debug)]
pub struct LoginOutData {
    pub id: String,
    pub username: String,
    pub token: String,
    pub exp: i64,
}
#[endpoint(tags("auth"))]
pub async fn post_login(
    idata: JsonBody<LoginInData>,
    depot: &mut Depot,
    res: &mut Response,
) -> JsonResult<LoginOutData> {
    let idata = idata.into_inner();
    let conn = db::pool();
    let Some(Model {
        id,
        username,
        password,
    }) = Users::find()
        .filter(users::Column::Username.eq(&idata.username))
        .one(conn)
        .await?
    else {
        return Err(StatusError::unauthorized()
            .brief("User does not exist.")
            .into());
    };

    if utils::verify_password(&idata.password, &password).is_err()
    {
        return Err(StatusError::unauthorized()
            .brief("Account not exist or password is incorrect.")
            .into());
    }

    // Create user session
    if let Some(session) = depot.session_mut() {
        session.login_user(&id, &username, &vec!["ROLE_USER".to_string()]);
    }

    // Set current user in depot
    let user = AuthUser::new(id.clone(), username.clone(), vec!["ROLE_USER".to_string()]);
    depot.set_current_user(user);

    // Generate JWT token
    let (token, exp) = jwt::get_token(&id)?;

    // Set JWT cookie
    let cookie = Cookie::build(("jwt_token", token.clone()))
        .path("/")
        .http_only(true)
        .build();
    res.add_cookie(cookie);

    // Handle remember me
    if idata.remember_me {
        let config = crate::config::get();
        res.set_remember_me_cookie(&username, &config.security).ok();
    }

    let odata = LoginOutData {
        id,
        username,
        token,
        exp,
    };

    json_ok(odata)
}

#[handler]
pub async fn logout(
    depot: &mut Depot,
    res: &mut Response,
) -> AppResult<()> {
    // Clear session
    if let Some(session) = depot.session_mut() {
        session.logout_user();
    }

    // Clear current user
    depot.clear_current_user();

    // Clear JWT cookie
    let jwt_cookie = Cookie::build(("jwt_token", ""))
        .path("/")
        .max_age(cookie::time::Duration::seconds(-1))
        .build();
    res.add_cookie(jwt_cookie);

    // Clear remember me cookie
    let config = crate::config::get();
    res.clear_remember_me_cookie(&config.security);

    // Redirect to login page
    res.render(Redirect::other("/login"));
    Ok(())
}

#[handler]
pub async fn logout_json(
    depot: &mut Depot,
    res: &mut Response,
) -> JsonResult<()> {
    // Clear session
    if let Some(session) = depot.session_mut() {
        session.logout_user();
    }

    // Clear current user
    depot.clear_current_user();

    // Clear JWT cookie
    let jwt_cookie = Cookie::build(("jwt_token", ""))
        .path("/")
        .max_age(cookie::time::Duration::seconds(-1))
        .build();
    res.add_cookie(jwt_cookie);

    // Clear remember me cookie
    let config = crate::config::get();
    res.clear_remember_me_cookie(&config.security);

    json_ok(())
}
