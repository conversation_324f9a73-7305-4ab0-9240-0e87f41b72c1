use rust_embed::RustEmbed;
use salvo::prelude::*;
use salvo::serve_static::{static_embed, EmbeddedFileExt};

mod auth;
mod demo;
mod user;
mod security;

use crate::{config, hoops};

#[derive(RustEmbed)]
#[folder = "assets"]
struct Assets;

pub fn root() -> Router {
    let favicon = Assets::get("favicon.ico")
        .expect("favicon not found")
        .into_handler();

    let config = config::get();

    // Create security middleware
    let session_handler = hoops::security::create_session_handler(&config.security);
    let auth_middleware = hoops::security::AuthMiddleware;
    let remember_me_middleware = hoops::security::RememberMeMiddleware::new();
    let anonymous_middleware = hoops::security::AnonymousUserMiddleware::new(config.security.clone());
    let security_headers = hoops::security::SecurityHeadersMiddleware::new(config.security.clone());

    // Create CSRF handler if enabled
    // Note: CSRF implementation is simplified for now
    // let csrf_handler = hoops::security::create_csrf_handler(&config.security);

    // Public routes (no authentication required)
    let public_routes = Router::new()
        .push(Router::with_path("login")
            .hoop(hoops::security::AnonymousOnlyMiddleware::new("/".to_string()))
            .get(auth::login_page)
        )
        .push(Router::with_path("signup")
            .hoop(hoops::security::AnonymousOnlyMiddleware::new("/".to_string()))
            .get(demo::hello) // Placeholder for signup page
        );

    // Authentication routes
    let auth_routes = Router::new()
        .push(Router::with_path("login").post(auth::post_login))
        .push(Router::with_path("logout")
            .get(auth::logout)
            .post(auth::logout_json)
        );

    // Protected routes (require authentication)
    let protected_routes = Router::new()
        .hoop(hoops::security::RequireAuthMiddleware::new())
        .push(Router::with_path("users").get(user::list_page))
        .push(Router::with_path("console").get(demo::hello)) // Admin console placeholder
        .push(Router::with_path("uc").get(demo::hello)); // User center placeholder

    // API routes
    let api_routes = Router::new()
        .push(Router::with_path("auth").push(auth_routes))
        .push(Router::with_path("security")
            .get(security::security_info)
            .push(Router::with_path("user").get(security::current_user))
            .push(Router::with_path("check-role").post(security::check_role))
            .push(Router::with_path("check-any-role").post(security::check_any_role))
        )
        .push(
            Router::with_path("users")
                .hoop(hoops::security::RequireAuthMiddleware::new())
                .hoop(hoops::auth_hoop(&config.jwt))
                .get(user::list_users)
                .post(user::create_user)
                .push(
                    Router::with_path("{user_id}")
                        .put(user::update_user)
                        .delete(user::delete_user),
                ),
        )
        .push(
            Router::with_path("admin")
                .hoop(hoops::security::RequireRoleMiddleware::new(vec!["ROLE_ADMIN"]))
                .get(security::admin_only)
        );

    // Main router with security middleware chain
    let mut router = Router::new()
        .hoop(Logger::new())
        .hoop(security_headers)
        .hoop(session_handler)
        .hoop(remember_me_middleware)
        .hoop(auth_middleware)
        .hoop(anonymous_middleware)
        .get(demo::hello)
        .push(public_routes)
        .push(protected_routes)
        .push(Router::with_path("api").push(api_routes))
        .push(Router::with_path("favicon.ico").get(favicon))
        .push(Router::with_path("assets/{**rest}").get(static_embed::<Assets>()));

    // Add CSRF protection if enabled
    // Note: CSRF implementation is simplified for now
    // if let Some(csrf) = csrf_handler {
    //     router = router.hoop(csrf);
    // }

    let doc = OpenApi::new("salvo web api", "0.0.1").merge_router(&router);
    router
        .unshift(doc.into_router("/api-doc/openapi.json"))
        .unshift(Scalar::new("/api-doc/openapi.json").into_router("scalar"))
}
