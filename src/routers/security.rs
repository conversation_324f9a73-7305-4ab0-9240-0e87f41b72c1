use salvo::prelude::*;
use salvo::oapi::extract::JsonBody;
use serde::{Deserialize, Serialize};
use crate::hoops::security::AuthDepotExt;
use crate::{json_ok, JsonResult};

#[derive(Serialize, ToSchema)]
pub struct UserInfo {
    pub id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub is_authenticated: bool,
    pub is_anonymous: bool,
}

#[derive(Serialize, ToSchema)]
pub struct SecurityInfo {
    pub csrf_token: Option<String>,
    pub session_id: Option<String>,
    pub user: Option<UserInfo>,
}

/// Get current user information
#[endpoint(tags("security"))]
pub async fn current_user(depot: &mut Depot) -> JsonResult<Option<UserInfo>> {
    if let Some(user) = depot.current_user() {
        let user_info = UserInfo {
            id: user.id.clone(),
            username: user.username.clone(),
            roles: user.roles.clone(),
            is_authenticated: user.is_authenticated(),
            is_anonymous: user.is_anonymous,
        };
        json_ok(Some(user_info))
    } else {
        json_ok(None)
    }
}

/// Get security information including CSRF token
#[endpoint(tags("security"))]
pub async fn security_info(depot: &mut Depot) -> JsonResult<SecurityInfo> {
    let user_info = if let Some(user) = depot.current_user() {
        Some(UserInfo {
            id: user.id.clone(),
            username: user.username.clone(),
            roles: user.roles.clone(),
            is_authenticated: user.is_authenticated(),
            is_anonymous: user.is_anonymous,
        })
    } else {
        None
    };

    let csrf_token = depot.get::<String>("csrf_token").ok().cloned();
    let session_id = depot.session()
        .map(|_s| "session_active".to_string()); // Simplified session ID

    let info = SecurityInfo {
        csrf_token,
        session_id,
        user: user_info,
    };

    json_ok(info)
}

/// Check if user has specific role
#[derive(Deserialize, ToSchema)]
pub struct RoleCheckRequest {
    pub role: String,
}

#[endpoint(tags("security"))]
pub async fn check_role(
    depot: &mut Depot,
    req: JsonBody<RoleCheckRequest>,
) -> JsonResult<bool> {
    let role_req = req.into_inner();
    let has_role = depot.has_role(&role_req.role);
    json_ok(has_role)
}

/// Check if user has any of the specified roles
#[derive(Deserialize, ToSchema)]
pub struct RolesCheckRequest {
    pub roles: Vec<String>,
}

#[endpoint(tags("security"))]
pub async fn check_any_role(
    depot: &mut Depot,
    req: JsonBody<RolesCheckRequest>,
) -> JsonResult<bool> {
    let roles_req = req.into_inner();
    let role_refs: Vec<&str> = roles_req.roles.iter().map(|s| s.as_str()).collect();
    let has_any_role = depot.has_any_role(&role_refs);
    json_ok(has_any_role)
}

/// Admin only endpoint example
#[endpoint(tags("security"))]
pub async fn admin_only(depot: &mut Depot) -> JsonResult<&'static str> {
    if !depot.has_role("ROLE_ADMIN") {
        return Err(StatusError::forbidden().into());
    }
    json_ok("Admin access granted")
}

/// User or admin endpoint example
#[endpoint(tags("security"))]
pub async fn user_or_admin(depot: &mut Depot) -> JsonResult<&'static str> {
    if !depot.has_any_role(&["ROLE_USER", "ROLE_ADMIN"]) {
        return Err(StatusError::forbidden().into());
    }
    json_ok("User or admin access granted")
}
