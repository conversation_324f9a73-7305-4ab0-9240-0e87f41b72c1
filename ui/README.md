## README

<p align="center">
    <a href="https://www.halo.run" target="_blank" rel="noopener noreferrer">
        <img width="100" src="https://www.halo.run/logo" alt="Halo logo" />
    </a>
</p>

> Halo 2.0 的管理端项目（原 halo-admin）

<p align="center">
<a href="https://github.com/halo-dev/console/releases"><img alt="GitHub release" src="https://img.shields.io/github/release/halo-dev/console.svg?style=flat-square" /></a>
<a href="https://github.com/halo-dev/console/blob/master/LICENSE"><img alt="GitHub" src="https://img.shields.io/github/license/halo-dev/console?style=flat-square"></a>
<a href="https://github.com/halo-dev/console/commits"><img alt="GitHub last commit" src="https://img.shields.io/github/last-commit/halo-dev/console.svg?style=flat-square"></a>
<a href="https://github.com/halo-dev/console/actions"><img alt="GitHub Workflow Status" src="https://img.shields.io/github/actions/workflow/status/halo-dev/console/main.yml?branch=main&style=flat-square"/></a>
<a href="https://gitpod.io/#https://github.com/halo-dev/console"><img alt="Gitpod ready-to-code" src="https://img.shields.io/badge/Gitpod-ready--to--code-blue?logo=gitpod&style=flat-square"/></a>
</p>

---

当前仓库已经将 `halo-admin` 改为了 `console`。但对于 Halo 1.x 版本，依旧保持 halo-admin 的概念。

## 开发环境运行

```bash
npm install -g pnpm@10
```

```bash
pnpm install
```

```bash
pnpm build:packages
```

```bash
pnpm dev
```

## 生产构建

```bash
pnpm build
```

## 状态

![Repobeats analytics](https://repobeats.axiom.co/api/embed/2db66f0e740d300f1bc6417d4465594755a5545d.svg "Repobeats analytics image")
