/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import { AuthProvider } from '../models';
// @ts-ignore
import { ListedAuthProvider } from '../models';
/**
 * AuthProviderV1alpha1ConsoleApi - axios parameter creator
 * @export
 */
export const AuthProviderV1alpha1ConsoleApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Disables an auth provider
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        disableAuthProvider: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('disableAuthProvider', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/auth-providers/{name}/disable`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Enables an auth provider
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        enableAuthProvider: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('enableAuthProvider', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/auth-providers/{name}/enable`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Lists all auth providers
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listAuthProviders: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/auth-providers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AuthProviderV1alpha1ConsoleApi - functional programming interface
 * @export
 */
export const AuthProviderV1alpha1ConsoleApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AuthProviderV1alpha1ConsoleApiAxiosParamCreator(configuration)
    return {
        /**
         * Disables an auth provider
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async disableAuthProvider(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AuthProvider>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.disableAuthProvider(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthProviderV1alpha1ConsoleApi.disableAuthProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Enables an auth provider
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async enableAuthProvider(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AuthProvider>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.enableAuthProvider(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthProviderV1alpha1ConsoleApi.enableAuthProvider']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Lists all auth providers
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listAuthProviders(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ListedAuthProvider>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listAuthProviders(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AuthProviderV1alpha1ConsoleApi.listAuthProviders']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AuthProviderV1alpha1ConsoleApi - factory interface
 * @export
 */
export const AuthProviderV1alpha1ConsoleApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AuthProviderV1alpha1ConsoleApiFp(configuration)
    return {
        /**
         * Disables an auth provider
         * @param {AuthProviderV1alpha1ConsoleApiDisableAuthProviderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        disableAuthProvider(requestParameters: AuthProviderV1alpha1ConsoleApiDisableAuthProviderRequest, options?: RawAxiosRequestConfig): AxiosPromise<AuthProvider> {
            return localVarFp.disableAuthProvider(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Enables an auth provider
         * @param {AuthProviderV1alpha1ConsoleApiEnableAuthProviderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        enableAuthProvider(requestParameters: AuthProviderV1alpha1ConsoleApiEnableAuthProviderRequest, options?: RawAxiosRequestConfig): AxiosPromise<AuthProvider> {
            return localVarFp.enableAuthProvider(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Lists all auth providers
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listAuthProviders(options?: RawAxiosRequestConfig): AxiosPromise<Array<ListedAuthProvider>> {
            return localVarFp.listAuthProviders(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for disableAuthProvider operation in AuthProviderV1alpha1ConsoleApi.
 * @export
 * @interface AuthProviderV1alpha1ConsoleApiDisableAuthProviderRequest
 */
export interface AuthProviderV1alpha1ConsoleApiDisableAuthProviderRequest {
    /**
     * 
     * @type {string}
     * @memberof AuthProviderV1alpha1ConsoleApiDisableAuthProvider
     */
    readonly name: string
}

/**
 * Request parameters for enableAuthProvider operation in AuthProviderV1alpha1ConsoleApi.
 * @export
 * @interface AuthProviderV1alpha1ConsoleApiEnableAuthProviderRequest
 */
export interface AuthProviderV1alpha1ConsoleApiEnableAuthProviderRequest {
    /**
     * 
     * @type {string}
     * @memberof AuthProviderV1alpha1ConsoleApiEnableAuthProvider
     */
    readonly name: string
}

/**
 * AuthProviderV1alpha1ConsoleApi - object-oriented interface
 * @export
 * @class AuthProviderV1alpha1ConsoleApi
 * @extends {BaseAPI}
 */
export class AuthProviderV1alpha1ConsoleApi extends BaseAPI {
    /**
     * Disables an auth provider
     * @param {AuthProviderV1alpha1ConsoleApiDisableAuthProviderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthProviderV1alpha1ConsoleApi
     */
    public disableAuthProvider(requestParameters: AuthProviderV1alpha1ConsoleApiDisableAuthProviderRequest, options?: RawAxiosRequestConfig) {
        return AuthProviderV1alpha1ConsoleApiFp(this.configuration).disableAuthProvider(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Enables an auth provider
     * @param {AuthProviderV1alpha1ConsoleApiEnableAuthProviderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthProviderV1alpha1ConsoleApi
     */
    public enableAuthProvider(requestParameters: AuthProviderV1alpha1ConsoleApiEnableAuthProviderRequest, options?: RawAxiosRequestConfig) {
        return AuthProviderV1alpha1ConsoleApiFp(this.configuration).enableAuthProvider(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Lists all auth providers
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AuthProviderV1alpha1ConsoleApi
     */
    public listAuthProviders(options?: RawAxiosRequestConfig) {
        return AuthProviderV1alpha1ConsoleApiFp(this.configuration).listAuthProviders(options).then((request) => request(this.axios, this.basePath));
    }
}

