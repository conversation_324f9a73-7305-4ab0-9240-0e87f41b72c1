/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import { Backup } from '../models';
// @ts-ignore
import { BackupList } from '../models';
// @ts-ignore
import { JsonPatchInner } from '../models';
/**
 * BackupV1alpha1Api - axios parameter creator
 * @export
 */
export const BackupV1alpha1ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create Backup
         * @param {Backup} [backup] Fresh backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createBackup: async (backup?: Backup, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/migration.halo.run/v1alpha1/backups`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backup, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete Backup
         * @param {string} name Name of backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteBackup: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('deleteBackup', 'name', name)
            const localVarPath = `/apis/migration.halo.run/v1alpha1/backups/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get Backup
         * @param {string} name Name of backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBackup: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('getBackup', 'name', name)
            const localVarPath = `/apis/migration.halo.run/v1alpha1/backups/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * List Backup
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listBackup: async (page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/migration.halo.run/v1alpha1/backups`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (labelSelector) {
                localVarQueryParameter['labelSelector'] = labelSelector;
            }

            if (fieldSelector) {
                localVarQueryParameter['fieldSelector'] = fieldSelector;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Patch Backup
         * @param {string} name Name of backup
         * @param {Array<JsonPatchInner>} [jsonPatchInner] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        patchBackup: async (name: string, jsonPatchInner?: Array<JsonPatchInner>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('patchBackup', 'name', name)
            const localVarPath = `/apis/migration.halo.run/v1alpha1/backups/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(jsonPatchInner, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update Backup
         * @param {string} name Name of backup
         * @param {Backup} [backup] Updated backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateBackup: async (name: string, backup?: Backup, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('updateBackup', 'name', name)
            const localVarPath = `/apis/migration.halo.run/v1alpha1/backups/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(backup, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * BackupV1alpha1Api - functional programming interface
 * @export
 */
export const BackupV1alpha1ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = BackupV1alpha1ApiAxiosParamCreator(configuration)
    return {
        /**
         * Create Backup
         * @param {Backup} [backup] Fresh backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createBackup(backup?: Backup, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Backup>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createBackup(backup, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BackupV1alpha1Api.createBackup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete Backup
         * @param {string} name Name of backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteBackup(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteBackup(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BackupV1alpha1Api.deleteBackup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get Backup
         * @param {string} name Name of backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getBackup(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Backup>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getBackup(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BackupV1alpha1Api.getBackup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * List Backup
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listBackup(page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<BackupList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listBackup(page, size, labelSelector, fieldSelector, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BackupV1alpha1Api.listBackup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Patch Backup
         * @param {string} name Name of backup
         * @param {Array<JsonPatchInner>} [jsonPatchInner] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async patchBackup(name: string, jsonPatchInner?: Array<JsonPatchInner>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Backup>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.patchBackup(name, jsonPatchInner, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BackupV1alpha1Api.patchBackup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update Backup
         * @param {string} name Name of backup
         * @param {Backup} [backup] Updated backup
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateBackup(name: string, backup?: Backup, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Backup>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateBackup(name, backup, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['BackupV1alpha1Api.updateBackup']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * BackupV1alpha1Api - factory interface
 * @export
 */
export const BackupV1alpha1ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = BackupV1alpha1ApiFp(configuration)
    return {
        /**
         * Create Backup
         * @param {BackupV1alpha1ApiCreateBackupRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createBackup(requestParameters: BackupV1alpha1ApiCreateBackupRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Backup> {
            return localVarFp.createBackup(requestParameters.backup, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete Backup
         * @param {BackupV1alpha1ApiDeleteBackupRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteBackup(requestParameters: BackupV1alpha1ApiDeleteBackupRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteBackup(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Get Backup
         * @param {BackupV1alpha1ApiGetBackupRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getBackup(requestParameters: BackupV1alpha1ApiGetBackupRequest, options?: RawAxiosRequestConfig): AxiosPromise<Backup> {
            return localVarFp.getBackup(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * List Backup
         * @param {BackupV1alpha1ApiListBackupRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listBackup(requestParameters: BackupV1alpha1ApiListBackupRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<BackupList> {
            return localVarFp.listBackup(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * Patch Backup
         * @param {BackupV1alpha1ApiPatchBackupRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        patchBackup(requestParameters: BackupV1alpha1ApiPatchBackupRequest, options?: RawAxiosRequestConfig): AxiosPromise<Backup> {
            return localVarFp.patchBackup(requestParameters.name, requestParameters.jsonPatchInner, options).then((request) => request(axios, basePath));
        },
        /**
         * Update Backup
         * @param {BackupV1alpha1ApiUpdateBackupRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateBackup(requestParameters: BackupV1alpha1ApiUpdateBackupRequest, options?: RawAxiosRequestConfig): AxiosPromise<Backup> {
            return localVarFp.updateBackup(requestParameters.name, requestParameters.backup, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createBackup operation in BackupV1alpha1Api.
 * @export
 * @interface BackupV1alpha1ApiCreateBackupRequest
 */
export interface BackupV1alpha1ApiCreateBackupRequest {
    /**
     * Fresh backup
     * @type {Backup}
     * @memberof BackupV1alpha1ApiCreateBackup
     */
    readonly backup?: Backup
}

/**
 * Request parameters for deleteBackup operation in BackupV1alpha1Api.
 * @export
 * @interface BackupV1alpha1ApiDeleteBackupRequest
 */
export interface BackupV1alpha1ApiDeleteBackupRequest {
    /**
     * Name of backup
     * @type {string}
     * @memberof BackupV1alpha1ApiDeleteBackup
     */
    readonly name: string
}

/**
 * Request parameters for getBackup operation in BackupV1alpha1Api.
 * @export
 * @interface BackupV1alpha1ApiGetBackupRequest
 */
export interface BackupV1alpha1ApiGetBackupRequest {
    /**
     * Name of backup
     * @type {string}
     * @memberof BackupV1alpha1ApiGetBackup
     */
    readonly name: string
}

/**
 * Request parameters for listBackup operation in BackupV1alpha1Api.
 * @export
 * @interface BackupV1alpha1ApiListBackupRequest
 */
export interface BackupV1alpha1ApiListBackupRequest {
    /**
     * Page number. Default is 0.
     * @type {number}
     * @memberof BackupV1alpha1ApiListBackup
     */
    readonly page?: number

    /**
     * Size number. Default is 0.
     * @type {number}
     * @memberof BackupV1alpha1ApiListBackup
     */
    readonly size?: number

    /**
     * Label selector. e.g.: hidden!&#x3D;true
     * @type {Array<string>}
     * @memberof BackupV1alpha1ApiListBackup
     */
    readonly labelSelector?: Array<string>

    /**
     * Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
     * @type {Array<string>}
     * @memberof BackupV1alpha1ApiListBackup
     */
    readonly fieldSelector?: Array<string>

    /**
     * Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @type {Array<string>}
     * @memberof BackupV1alpha1ApiListBackup
     */
    readonly sort?: Array<string>
}

/**
 * Request parameters for patchBackup operation in BackupV1alpha1Api.
 * @export
 * @interface BackupV1alpha1ApiPatchBackupRequest
 */
export interface BackupV1alpha1ApiPatchBackupRequest {
    /**
     * Name of backup
     * @type {string}
     * @memberof BackupV1alpha1ApiPatchBackup
     */
    readonly name: string

    /**
     * 
     * @type {Array<JsonPatchInner>}
     * @memberof BackupV1alpha1ApiPatchBackup
     */
    readonly jsonPatchInner?: Array<JsonPatchInner>
}

/**
 * Request parameters for updateBackup operation in BackupV1alpha1Api.
 * @export
 * @interface BackupV1alpha1ApiUpdateBackupRequest
 */
export interface BackupV1alpha1ApiUpdateBackupRequest {
    /**
     * Name of backup
     * @type {string}
     * @memberof BackupV1alpha1ApiUpdateBackup
     */
    readonly name: string

    /**
     * Updated backup
     * @type {Backup}
     * @memberof BackupV1alpha1ApiUpdateBackup
     */
    readonly backup?: Backup
}

/**
 * BackupV1alpha1Api - object-oriented interface
 * @export
 * @class BackupV1alpha1Api
 * @extends {BaseAPI}
 */
export class BackupV1alpha1Api extends BaseAPI {
    /**
     * Create Backup
     * @param {BackupV1alpha1ApiCreateBackupRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BackupV1alpha1Api
     */
    public createBackup(requestParameters: BackupV1alpha1ApiCreateBackupRequest = {}, options?: RawAxiosRequestConfig) {
        return BackupV1alpha1ApiFp(this.configuration).createBackup(requestParameters.backup, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete Backup
     * @param {BackupV1alpha1ApiDeleteBackupRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BackupV1alpha1Api
     */
    public deleteBackup(requestParameters: BackupV1alpha1ApiDeleteBackupRequest, options?: RawAxiosRequestConfig) {
        return BackupV1alpha1ApiFp(this.configuration).deleteBackup(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get Backup
     * @param {BackupV1alpha1ApiGetBackupRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BackupV1alpha1Api
     */
    public getBackup(requestParameters: BackupV1alpha1ApiGetBackupRequest, options?: RawAxiosRequestConfig) {
        return BackupV1alpha1ApiFp(this.configuration).getBackup(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * List Backup
     * @param {BackupV1alpha1ApiListBackupRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BackupV1alpha1Api
     */
    public listBackup(requestParameters: BackupV1alpha1ApiListBackupRequest = {}, options?: RawAxiosRequestConfig) {
        return BackupV1alpha1ApiFp(this.configuration).listBackup(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Patch Backup
     * @param {BackupV1alpha1ApiPatchBackupRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BackupV1alpha1Api
     */
    public patchBackup(requestParameters: BackupV1alpha1ApiPatchBackupRequest, options?: RawAxiosRequestConfig) {
        return BackupV1alpha1ApiFp(this.configuration).patchBackup(requestParameters.name, requestParameters.jsonPatchInner, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update Backup
     * @param {BackupV1alpha1ApiUpdateBackupRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof BackupV1alpha1Api
     */
    public updateBackup(requestParameters: BackupV1alpha1ApiUpdateBackupRequest, options?: RawAxiosRequestConfig) {
        return BackupV1alpha1ApiFp(this.configuration).updateBackup(requestParameters.name, requestParameters.backup, options).then((request) => request(this.axios, this.basePath));
    }
}

