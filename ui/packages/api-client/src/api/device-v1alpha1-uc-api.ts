/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import { UserDevice } from '../models';
/**
 * DeviceV1alpha1UcApi - axios parameter creator
 * @export
 */
export const DeviceV1alpha1UcApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * List all user devices
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDevices: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/devices`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Revoke a own device
         * @param {string} deviceId Device ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokeDevice: async (deviceId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'deviceId' is not null or undefined
            assertParamExists('revokeDevice', 'deviceId', deviceId)
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/devices/{deviceId}`
                .replace(`{${"deviceId"}}`, encodeURIComponent(String(deviceId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DeviceV1alpha1UcApi - functional programming interface
 * @export
 */
export const DeviceV1alpha1UcApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DeviceV1alpha1UcApiAxiosParamCreator(configuration)
    return {
        /**
         * List all user devices
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listDevices(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<UserDevice>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listDevices(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DeviceV1alpha1UcApi.listDevices']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Revoke a own device
         * @param {string} deviceId Device ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async revokeDevice(deviceId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.revokeDevice(deviceId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DeviceV1alpha1UcApi.revokeDevice']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DeviceV1alpha1UcApi - factory interface
 * @export
 */
export const DeviceV1alpha1UcApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DeviceV1alpha1UcApiFp(configuration)
    return {
        /**
         * List all user devices
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDevices(options?: RawAxiosRequestConfig): AxiosPromise<Array<UserDevice>> {
            return localVarFp.listDevices(options).then((request) => request(axios, basePath));
        },
        /**
         * Revoke a own device
         * @param {DeviceV1alpha1UcApiRevokeDeviceRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokeDevice(requestParameters: DeviceV1alpha1UcApiRevokeDeviceRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.revokeDevice(requestParameters.deviceId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for revokeDevice operation in DeviceV1alpha1UcApi.
 * @export
 * @interface DeviceV1alpha1UcApiRevokeDeviceRequest
 */
export interface DeviceV1alpha1UcApiRevokeDeviceRequest {
    /**
     * Device ID
     * @type {string}
     * @memberof DeviceV1alpha1UcApiRevokeDevice
     */
    readonly deviceId: string
}

/**
 * DeviceV1alpha1UcApi - object-oriented interface
 * @export
 * @class DeviceV1alpha1UcApi
 * @extends {BaseAPI}
 */
export class DeviceV1alpha1UcApi extends BaseAPI {
    /**
     * List all user devices
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceV1alpha1UcApi
     */
    public listDevices(options?: RawAxiosRequestConfig) {
        return DeviceV1alpha1UcApiFp(this.configuration).listDevices(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Revoke a own device
     * @param {DeviceV1alpha1UcApiRevokeDeviceRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DeviceV1alpha1UcApi
     */
    public revokeDevice(requestParameters: DeviceV1alpha1UcApiRevokeDeviceRequest, options?: RawAxiosRequestConfig) {
        return DeviceV1alpha1UcApiFp(this.configuration).revokeDevice(requestParameters.deviceId, options).then((request) => request(this.axios, this.basePath));
    }
}

