/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import { JsonPatchInner } from '../models';
// @ts-ignore
import { NotificationTemplate } from '../models';
// @ts-ignore
import { NotificationTemplateList } from '../models';
/**
 * NotificationTemplateV1alpha1Api - axios parameter creator
 * @export
 */
export const NotificationTemplateV1alpha1ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create NotificationTemplate
         * @param {NotificationTemplate} [notificationTemplate] Fresh notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createNotificationTemplate: async (notificationTemplate?: NotificationTemplate, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/notification.halo.run/v1alpha1/notificationtemplates`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(notificationTemplate, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNotificationTemplate: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('deleteNotificationTemplate', 'name', name)
            const localVarPath = `/apis/notification.halo.run/v1alpha1/notificationtemplates/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNotificationTemplate: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('getNotificationTemplate', 'name', name)
            const localVarPath = `/apis/notification.halo.run/v1alpha1/notificationtemplates/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * List NotificationTemplate
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listNotificationTemplate: async (page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/notification.halo.run/v1alpha1/notificationtemplates`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (labelSelector) {
                localVarQueryParameter['labelSelector'] = labelSelector;
            }

            if (fieldSelector) {
                localVarQueryParameter['fieldSelector'] = fieldSelector;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Patch NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {Array<JsonPatchInner>} [jsonPatchInner] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        patchNotificationTemplate: async (name: string, jsonPatchInner?: Array<JsonPatchInner>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('patchNotificationTemplate', 'name', name)
            const localVarPath = `/apis/notification.halo.run/v1alpha1/notificationtemplates/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(jsonPatchInner, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {NotificationTemplate} [notificationTemplate] Updated notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateNotificationTemplate: async (name: string, notificationTemplate?: NotificationTemplate, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('updateNotificationTemplate', 'name', name)
            const localVarPath = `/apis/notification.halo.run/v1alpha1/notificationtemplates/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(notificationTemplate, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NotificationTemplateV1alpha1Api - functional programming interface
 * @export
 */
export const NotificationTemplateV1alpha1ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = NotificationTemplateV1alpha1ApiAxiosParamCreator(configuration)
    return {
        /**
         * Create NotificationTemplate
         * @param {NotificationTemplate} [notificationTemplate] Fresh notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createNotificationTemplate(notificationTemplate?: NotificationTemplate, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NotificationTemplate>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createNotificationTemplate(notificationTemplate, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NotificationTemplateV1alpha1Api.createNotificationTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteNotificationTemplate(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteNotificationTemplate(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NotificationTemplateV1alpha1Api.deleteNotificationTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getNotificationTemplate(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NotificationTemplate>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getNotificationTemplate(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NotificationTemplateV1alpha1Api.getNotificationTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * List NotificationTemplate
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listNotificationTemplate(page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NotificationTemplateList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listNotificationTemplate(page, size, labelSelector, fieldSelector, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NotificationTemplateV1alpha1Api.listNotificationTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Patch NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {Array<JsonPatchInner>} [jsonPatchInner] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async patchNotificationTemplate(name: string, jsonPatchInner?: Array<JsonPatchInner>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NotificationTemplate>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.patchNotificationTemplate(name, jsonPatchInner, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NotificationTemplateV1alpha1Api.patchNotificationTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update NotificationTemplate
         * @param {string} name Name of notificationtemplate
         * @param {NotificationTemplate} [notificationTemplate] Updated notificationtemplate
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateNotificationTemplate(name: string, notificationTemplate?: NotificationTemplate, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<NotificationTemplate>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateNotificationTemplate(name, notificationTemplate, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['NotificationTemplateV1alpha1Api.updateNotificationTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * NotificationTemplateV1alpha1Api - factory interface
 * @export
 */
export const NotificationTemplateV1alpha1ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = NotificationTemplateV1alpha1ApiFp(configuration)
    return {
        /**
         * Create NotificationTemplate
         * @param {NotificationTemplateV1alpha1ApiCreateNotificationTemplateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiCreateNotificationTemplateRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<NotificationTemplate> {
            return localVarFp.createNotificationTemplate(requestParameters.notificationTemplate, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete NotificationTemplate
         * @param {NotificationTemplateV1alpha1ApiDeleteNotificationTemplateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiDeleteNotificationTemplateRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteNotificationTemplate(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Get NotificationTemplate
         * @param {NotificationTemplateV1alpha1ApiGetNotificationTemplateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiGetNotificationTemplateRequest, options?: RawAxiosRequestConfig): AxiosPromise<NotificationTemplate> {
            return localVarFp.getNotificationTemplate(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * List NotificationTemplate
         * @param {NotificationTemplateV1alpha1ApiListNotificationTemplateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiListNotificationTemplateRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<NotificationTemplateList> {
            return localVarFp.listNotificationTemplate(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * Patch NotificationTemplate
         * @param {NotificationTemplateV1alpha1ApiPatchNotificationTemplateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        patchNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiPatchNotificationTemplateRequest, options?: RawAxiosRequestConfig): AxiosPromise<NotificationTemplate> {
            return localVarFp.patchNotificationTemplate(requestParameters.name, requestParameters.jsonPatchInner, options).then((request) => request(axios, basePath));
        },
        /**
         * Update NotificationTemplate
         * @param {NotificationTemplateV1alpha1ApiUpdateNotificationTemplateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiUpdateNotificationTemplateRequest, options?: RawAxiosRequestConfig): AxiosPromise<NotificationTemplate> {
            return localVarFp.updateNotificationTemplate(requestParameters.name, requestParameters.notificationTemplate, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createNotificationTemplate operation in NotificationTemplateV1alpha1Api.
 * @export
 * @interface NotificationTemplateV1alpha1ApiCreateNotificationTemplateRequest
 */
export interface NotificationTemplateV1alpha1ApiCreateNotificationTemplateRequest {
    /**
     * Fresh notificationtemplate
     * @type {NotificationTemplate}
     * @memberof NotificationTemplateV1alpha1ApiCreateNotificationTemplate
     */
    readonly notificationTemplate?: NotificationTemplate
}

/**
 * Request parameters for deleteNotificationTemplate operation in NotificationTemplateV1alpha1Api.
 * @export
 * @interface NotificationTemplateV1alpha1ApiDeleteNotificationTemplateRequest
 */
export interface NotificationTemplateV1alpha1ApiDeleteNotificationTemplateRequest {
    /**
     * Name of notificationtemplate
     * @type {string}
     * @memberof NotificationTemplateV1alpha1ApiDeleteNotificationTemplate
     */
    readonly name: string
}

/**
 * Request parameters for getNotificationTemplate operation in NotificationTemplateV1alpha1Api.
 * @export
 * @interface NotificationTemplateV1alpha1ApiGetNotificationTemplateRequest
 */
export interface NotificationTemplateV1alpha1ApiGetNotificationTemplateRequest {
    /**
     * Name of notificationtemplate
     * @type {string}
     * @memberof NotificationTemplateV1alpha1ApiGetNotificationTemplate
     */
    readonly name: string
}

/**
 * Request parameters for listNotificationTemplate operation in NotificationTemplateV1alpha1Api.
 * @export
 * @interface NotificationTemplateV1alpha1ApiListNotificationTemplateRequest
 */
export interface NotificationTemplateV1alpha1ApiListNotificationTemplateRequest {
    /**
     * Page number. Default is 0.
     * @type {number}
     * @memberof NotificationTemplateV1alpha1ApiListNotificationTemplate
     */
    readonly page?: number

    /**
     * Size number. Default is 0.
     * @type {number}
     * @memberof NotificationTemplateV1alpha1ApiListNotificationTemplate
     */
    readonly size?: number

    /**
     * Label selector. e.g.: hidden!&#x3D;true
     * @type {Array<string>}
     * @memberof NotificationTemplateV1alpha1ApiListNotificationTemplate
     */
    readonly labelSelector?: Array<string>

    /**
     * Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
     * @type {Array<string>}
     * @memberof NotificationTemplateV1alpha1ApiListNotificationTemplate
     */
    readonly fieldSelector?: Array<string>

    /**
     * Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @type {Array<string>}
     * @memberof NotificationTemplateV1alpha1ApiListNotificationTemplate
     */
    readonly sort?: Array<string>
}

/**
 * Request parameters for patchNotificationTemplate operation in NotificationTemplateV1alpha1Api.
 * @export
 * @interface NotificationTemplateV1alpha1ApiPatchNotificationTemplateRequest
 */
export interface NotificationTemplateV1alpha1ApiPatchNotificationTemplateRequest {
    /**
     * Name of notificationtemplate
     * @type {string}
     * @memberof NotificationTemplateV1alpha1ApiPatchNotificationTemplate
     */
    readonly name: string

    /**
     * 
     * @type {Array<JsonPatchInner>}
     * @memberof NotificationTemplateV1alpha1ApiPatchNotificationTemplate
     */
    readonly jsonPatchInner?: Array<JsonPatchInner>
}

/**
 * Request parameters for updateNotificationTemplate operation in NotificationTemplateV1alpha1Api.
 * @export
 * @interface NotificationTemplateV1alpha1ApiUpdateNotificationTemplateRequest
 */
export interface NotificationTemplateV1alpha1ApiUpdateNotificationTemplateRequest {
    /**
     * Name of notificationtemplate
     * @type {string}
     * @memberof NotificationTemplateV1alpha1ApiUpdateNotificationTemplate
     */
    readonly name: string

    /**
     * Updated notificationtemplate
     * @type {NotificationTemplate}
     * @memberof NotificationTemplateV1alpha1ApiUpdateNotificationTemplate
     */
    readonly notificationTemplate?: NotificationTemplate
}

/**
 * NotificationTemplateV1alpha1Api - object-oriented interface
 * @export
 * @class NotificationTemplateV1alpha1Api
 * @extends {BaseAPI}
 */
export class NotificationTemplateV1alpha1Api extends BaseAPI {
    /**
     * Create NotificationTemplate
     * @param {NotificationTemplateV1alpha1ApiCreateNotificationTemplateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationTemplateV1alpha1Api
     */
    public createNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiCreateNotificationTemplateRequest = {}, options?: RawAxiosRequestConfig) {
        return NotificationTemplateV1alpha1ApiFp(this.configuration).createNotificationTemplate(requestParameters.notificationTemplate, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete NotificationTemplate
     * @param {NotificationTemplateV1alpha1ApiDeleteNotificationTemplateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationTemplateV1alpha1Api
     */
    public deleteNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiDeleteNotificationTemplateRequest, options?: RawAxiosRequestConfig) {
        return NotificationTemplateV1alpha1ApiFp(this.configuration).deleteNotificationTemplate(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get NotificationTemplate
     * @param {NotificationTemplateV1alpha1ApiGetNotificationTemplateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationTemplateV1alpha1Api
     */
    public getNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiGetNotificationTemplateRequest, options?: RawAxiosRequestConfig) {
        return NotificationTemplateV1alpha1ApiFp(this.configuration).getNotificationTemplate(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * List NotificationTemplate
     * @param {NotificationTemplateV1alpha1ApiListNotificationTemplateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationTemplateV1alpha1Api
     */
    public listNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiListNotificationTemplateRequest = {}, options?: RawAxiosRequestConfig) {
        return NotificationTemplateV1alpha1ApiFp(this.configuration).listNotificationTemplate(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Patch NotificationTemplate
     * @param {NotificationTemplateV1alpha1ApiPatchNotificationTemplateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationTemplateV1alpha1Api
     */
    public patchNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiPatchNotificationTemplateRequest, options?: RawAxiosRequestConfig) {
        return NotificationTemplateV1alpha1ApiFp(this.configuration).patchNotificationTemplate(requestParameters.name, requestParameters.jsonPatchInner, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update NotificationTemplate
     * @param {NotificationTemplateV1alpha1ApiUpdateNotificationTemplateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NotificationTemplateV1alpha1Api
     */
    public updateNotificationTemplate(requestParameters: NotificationTemplateV1alpha1ApiUpdateNotificationTemplateRequest, options?: RawAxiosRequestConfig) {
        return NotificationTemplateV1alpha1ApiFp(this.configuration).updateNotificationTemplate(requestParameters.name, requestParameters.notificationTemplate, options).then((request) => request(this.axios, this.basePath));
    }
}

