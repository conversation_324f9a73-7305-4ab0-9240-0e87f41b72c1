/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import { PersonalAccessToken } from '../models';
/**
 * PersonalAccessTokenV1alpha1UcApi - axios parameter creator
 * @export
 */
export const PersonalAccessTokenV1alpha1UcApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Delete a PAT
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePat: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('deletePat', 'name', name)
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Generate a PAT.
         * @param {PersonalAccessToken} personalAccessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generatePat: async (personalAccessToken: PersonalAccessToken, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'personalAccessToken' is not null or undefined
            assertParamExists('generatePat', 'personalAccessToken', personalAccessToken)
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(personalAccessToken, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Obtain a PAT.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        obtainPat: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('obtainPat', 'name', name)
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Obtain PAT list.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        obtainPats: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Restore a PAT.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        restorePat: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('restorePat', 'name', name)
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens/{name}/actions/restoration`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Revoke a PAT
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokePat: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('revokePat', 'name', name)
            const localVarPath = `/apis/uc.api.security.halo.run/v1alpha1/personalaccesstokens/{name}/actions/revocation`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PersonalAccessTokenV1alpha1UcApi - functional programming interface
 * @export
 */
export const PersonalAccessTokenV1alpha1UcApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = PersonalAccessTokenV1alpha1UcApiAxiosParamCreator(configuration)
    return {
        /**
         * Delete a PAT
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deletePat(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deletePat(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PersonalAccessTokenV1alpha1UcApi.deletePat']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Generate a PAT.
         * @param {PersonalAccessToken} personalAccessToken 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async generatePat(personalAccessToken: PersonalAccessToken, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PersonalAccessToken>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.generatePat(personalAccessToken, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PersonalAccessTokenV1alpha1UcApi.generatePat']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Obtain a PAT.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async obtainPat(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.obtainPat(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PersonalAccessTokenV1alpha1UcApi.obtainPat']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Obtain PAT list.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async obtainPats(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<PersonalAccessToken>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.obtainPats(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PersonalAccessTokenV1alpha1UcApi.obtainPats']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Restore a PAT.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async restorePat(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.restorePat(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PersonalAccessTokenV1alpha1UcApi.restorePat']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Revoke a PAT
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async revokePat(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.revokePat(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PersonalAccessTokenV1alpha1UcApi.revokePat']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * PersonalAccessTokenV1alpha1UcApi - factory interface
 * @export
 */
export const PersonalAccessTokenV1alpha1UcApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = PersonalAccessTokenV1alpha1UcApiFp(configuration)
    return {
        /**
         * Delete a PAT
         * @param {PersonalAccessTokenV1alpha1UcApiDeletePatRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePat(requestParameters: PersonalAccessTokenV1alpha1UcApiDeletePatRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deletePat(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Generate a PAT.
         * @param {PersonalAccessTokenV1alpha1UcApiGeneratePatRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generatePat(requestParameters: PersonalAccessTokenV1alpha1UcApiGeneratePatRequest, options?: RawAxiosRequestConfig): AxiosPromise<PersonalAccessToken> {
            return localVarFp.generatePat(requestParameters.personalAccessToken, options).then((request) => request(axios, basePath));
        },
        /**
         * Obtain a PAT.
         * @param {PersonalAccessTokenV1alpha1UcApiObtainPatRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        obtainPat(requestParameters: PersonalAccessTokenV1alpha1UcApiObtainPatRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.obtainPat(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Obtain PAT list.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        obtainPats(options?: RawAxiosRequestConfig): AxiosPromise<Array<PersonalAccessToken>> {
            return localVarFp.obtainPats(options).then((request) => request(axios, basePath));
        },
        /**
         * Restore a PAT.
         * @param {PersonalAccessTokenV1alpha1UcApiRestorePatRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        restorePat(requestParameters: PersonalAccessTokenV1alpha1UcApiRestorePatRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.restorePat(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Revoke a PAT
         * @param {PersonalAccessTokenV1alpha1UcApiRevokePatRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revokePat(requestParameters: PersonalAccessTokenV1alpha1UcApiRevokePatRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.revokePat(requestParameters.name, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for deletePat operation in PersonalAccessTokenV1alpha1UcApi.
 * @export
 * @interface PersonalAccessTokenV1alpha1UcApiDeletePatRequest
 */
export interface PersonalAccessTokenV1alpha1UcApiDeletePatRequest {
    /**
     * 
     * @type {string}
     * @memberof PersonalAccessTokenV1alpha1UcApiDeletePat
     */
    readonly name: string
}

/**
 * Request parameters for generatePat operation in PersonalAccessTokenV1alpha1UcApi.
 * @export
 * @interface PersonalAccessTokenV1alpha1UcApiGeneratePatRequest
 */
export interface PersonalAccessTokenV1alpha1UcApiGeneratePatRequest {
    /**
     * 
     * @type {PersonalAccessToken}
     * @memberof PersonalAccessTokenV1alpha1UcApiGeneratePat
     */
    readonly personalAccessToken: PersonalAccessToken
}

/**
 * Request parameters for obtainPat operation in PersonalAccessTokenV1alpha1UcApi.
 * @export
 * @interface PersonalAccessTokenV1alpha1UcApiObtainPatRequest
 */
export interface PersonalAccessTokenV1alpha1UcApiObtainPatRequest {
    /**
     * 
     * @type {string}
     * @memberof PersonalAccessTokenV1alpha1UcApiObtainPat
     */
    readonly name: string
}

/**
 * Request parameters for restorePat operation in PersonalAccessTokenV1alpha1UcApi.
 * @export
 * @interface PersonalAccessTokenV1alpha1UcApiRestorePatRequest
 */
export interface PersonalAccessTokenV1alpha1UcApiRestorePatRequest {
    /**
     * 
     * @type {string}
     * @memberof PersonalAccessTokenV1alpha1UcApiRestorePat
     */
    readonly name: string
}

/**
 * Request parameters for revokePat operation in PersonalAccessTokenV1alpha1UcApi.
 * @export
 * @interface PersonalAccessTokenV1alpha1UcApiRevokePatRequest
 */
export interface PersonalAccessTokenV1alpha1UcApiRevokePatRequest {
    /**
     * 
     * @type {string}
     * @memberof PersonalAccessTokenV1alpha1UcApiRevokePat
     */
    readonly name: string
}

/**
 * PersonalAccessTokenV1alpha1UcApi - object-oriented interface
 * @export
 * @class PersonalAccessTokenV1alpha1UcApi
 * @extends {BaseAPI}
 */
export class PersonalAccessTokenV1alpha1UcApi extends BaseAPI {
    /**
     * Delete a PAT
     * @param {PersonalAccessTokenV1alpha1UcApiDeletePatRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PersonalAccessTokenV1alpha1UcApi
     */
    public deletePat(requestParameters: PersonalAccessTokenV1alpha1UcApiDeletePatRequest, options?: RawAxiosRequestConfig) {
        return PersonalAccessTokenV1alpha1UcApiFp(this.configuration).deletePat(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Generate a PAT.
     * @param {PersonalAccessTokenV1alpha1UcApiGeneratePatRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PersonalAccessTokenV1alpha1UcApi
     */
    public generatePat(requestParameters: PersonalAccessTokenV1alpha1UcApiGeneratePatRequest, options?: RawAxiosRequestConfig) {
        return PersonalAccessTokenV1alpha1UcApiFp(this.configuration).generatePat(requestParameters.personalAccessToken, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Obtain a PAT.
     * @param {PersonalAccessTokenV1alpha1UcApiObtainPatRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PersonalAccessTokenV1alpha1UcApi
     */
    public obtainPat(requestParameters: PersonalAccessTokenV1alpha1UcApiObtainPatRequest, options?: RawAxiosRequestConfig) {
        return PersonalAccessTokenV1alpha1UcApiFp(this.configuration).obtainPat(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Obtain PAT list.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PersonalAccessTokenV1alpha1UcApi
     */
    public obtainPats(options?: RawAxiosRequestConfig) {
        return PersonalAccessTokenV1alpha1UcApiFp(this.configuration).obtainPats(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Restore a PAT.
     * @param {PersonalAccessTokenV1alpha1UcApiRestorePatRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PersonalAccessTokenV1alpha1UcApi
     */
    public restorePat(requestParameters: PersonalAccessTokenV1alpha1UcApiRestorePatRequest, options?: RawAxiosRequestConfig) {
        return PersonalAccessTokenV1alpha1UcApiFp(this.configuration).restorePat(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Revoke a PAT
     * @param {PersonalAccessTokenV1alpha1UcApiRevokePatRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PersonalAccessTokenV1alpha1UcApi
     */
    public revokePat(requestParameters: PersonalAccessTokenV1alpha1UcApiRevokePatRequest, options?: RawAxiosRequestConfig) {
        return PersonalAccessTokenV1alpha1UcApiFp(this.configuration).revokePat(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }
}

