/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import { Content } from '../models';
// @ts-ignore
import { ContentWrapper } from '../models';
// @ts-ignore
import { ListedPostList } from '../models';
// @ts-ignore
import { ListedSnapshotDto } from '../models';
// @ts-ignore
import { Post } from '../models';
// @ts-ignore
import { PostRequest } from '../models';
// @ts-ignore
import { RevertSnapshotForPostParam } from '../models';
/**
 * PostV1alpha1ConsoleApi - axios parameter creator
 * @export
 */
export const PostV1alpha1ConsoleApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Delete a content for post.
         * @param {string} name 
         * @param {string} snapshotName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePostContent: async (name: string, snapshotName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('deletePostContent', 'name', name)
            // verify required parameter 'snapshotName' is not null or undefined
            assertParamExists('deletePostContent', 'snapshotName', snapshotName)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/content`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (snapshotName !== undefined) {
                localVarQueryParameter['snapshotName'] = snapshotName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Draft a post.
         * @param {PostRequest} postRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        draftPost: async (postRequest: PostRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'postRequest' is not null or undefined
            assertParamExists('draftPost', 'postRequest', postRequest)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(postRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Fetch content of post.
         * @param {string} name 
         * @param {string} snapshotName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        fetchPostContent: async (name: string, snapshotName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('fetchPostContent', 'name', name)
            // verify required parameter 'snapshotName' is not null or undefined
            assertParamExists('fetchPostContent', 'snapshotName', snapshotName)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/content`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (snapshotName !== undefined) {
                localVarQueryParameter['snapshotName'] = snapshotName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Fetch head content of post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        fetchPostHeadContent: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('fetchPostHeadContent', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/head-content`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Fetch release content of post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        fetchPostReleaseContent: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('fetchPostReleaseContent', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/release-content`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * List all snapshots for post content.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPostSnapshots: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('listPostSnapshots', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/snapshot`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * List posts.
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {ListPostsPublishPhaseEnum} [publishPhase] Posts filtered by publish phase.
         * @param {string} [keyword] Posts filtered by keyword.
         * @param {string} [categoryWithChildren] Posts filtered by category including sub-categories.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPosts: async (page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, publishPhase?: ListPostsPublishPhaseEnum, keyword?: string, categoryWithChildren?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (labelSelector) {
                localVarQueryParameter['labelSelector'] = labelSelector;
            }

            if (fieldSelector) {
                localVarQueryParameter['fieldSelector'] = fieldSelector;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }

            if (publishPhase !== undefined) {
                localVarQueryParameter['publishPhase'] = publishPhase;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (categoryWithChildren !== undefined) {
                localVarQueryParameter['categoryWithChildren'] = categoryWithChildren;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Publish a post.
         * @param {string} name 
         * @param {string} [headSnapshot] Head snapshot name of content.
         * @param {boolean} [async] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        publishPost: async (name: string, headSnapshot?: string, async?: boolean, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('publishPost', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/publish`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (headSnapshot !== undefined) {
                localVarQueryParameter['headSnapshot'] = headSnapshot;
            }

            if (async !== undefined) {
                localVarQueryParameter['async'] = async;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Recycle a post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        recyclePost: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('recyclePost', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/recycle`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Revert to specified snapshot for post content.
         * @param {string} name 
         * @param {RevertSnapshotForPostParam} revertSnapshotForPostParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revertToSpecifiedSnapshotForPost: async (name: string, revertSnapshotForPostParam: RevertSnapshotForPostParam, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('revertToSpecifiedSnapshotForPost', 'name', name)
            // verify required parameter 'revertSnapshotForPostParam' is not null or undefined
            assertParamExists('revertToSpecifiedSnapshotForPost', 'revertSnapshotForPostParam', revertSnapshotForPostParam)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/revert-content`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(revertSnapshotForPostParam, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Publish a post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unpublishPost: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('unpublishPost', 'name', name)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/unpublish`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a post.
         * @param {string} name 
         * @param {PostRequest} postRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDraftPost: async (name: string, postRequest: PostRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('updateDraftPost', 'name', name)
            // verify required parameter 'postRequest' is not null or undefined
            assertParamExists('updateDraftPost', 'postRequest', postRequest)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(postRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update a post\'s content.
         * @param {string} name 
         * @param {Content} content 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePostContent: async (name: string, content: Content, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('updatePostContent', 'name', name)
            // verify required parameter 'content' is not null or undefined
            assertParamExists('updatePostContent', 'content', content)
            const localVarPath = `/apis/api.console.halo.run/v1alpha1/posts/{name}/content`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(content, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PostV1alpha1ConsoleApi - functional programming interface
 * @export
 */
export const PostV1alpha1ConsoleApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = PostV1alpha1ConsoleApiAxiosParamCreator(configuration)
    return {
        /**
         * Delete a content for post.
         * @param {string} name 
         * @param {string} snapshotName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deletePostContent(name: string, snapshotName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ContentWrapper>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deletePostContent(name, snapshotName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.deletePostContent']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Draft a post.
         * @param {PostRequest} postRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async draftPost(postRequest: PostRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Post>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.draftPost(postRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.draftPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Fetch content of post.
         * @param {string} name 
         * @param {string} snapshotName 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async fetchPostContent(name: string, snapshotName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ContentWrapper>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.fetchPostContent(name, snapshotName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.fetchPostContent']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Fetch head content of post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async fetchPostHeadContent(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ContentWrapper>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.fetchPostHeadContent(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.fetchPostHeadContent']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Fetch release content of post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async fetchPostReleaseContent(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ContentWrapper>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.fetchPostReleaseContent(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.fetchPostReleaseContent']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * List all snapshots for post content.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listPostSnapshots(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ListedSnapshotDto>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listPostSnapshots(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.listPostSnapshots']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * List posts.
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {ListPostsPublishPhaseEnum} [publishPhase] Posts filtered by publish phase.
         * @param {string} [keyword] Posts filtered by keyword.
         * @param {string} [categoryWithChildren] Posts filtered by category including sub-categories.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listPosts(page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, publishPhase?: ListPostsPublishPhaseEnum, keyword?: string, categoryWithChildren?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ListedPostList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listPosts(page, size, labelSelector, fieldSelector, sort, publishPhase, keyword, categoryWithChildren, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.listPosts']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Publish a post.
         * @param {string} name 
         * @param {string} [headSnapshot] Head snapshot name of content.
         * @param {boolean} [async] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async publishPost(name: string, headSnapshot?: string, async?: boolean, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Post>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.publishPost(name, headSnapshot, async, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.publishPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Recycle a post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async recyclePost(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.recyclePost(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.recyclePost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Revert to specified snapshot for post content.
         * @param {string} name 
         * @param {RevertSnapshotForPostParam} revertSnapshotForPostParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async revertToSpecifiedSnapshotForPost(name: string, revertSnapshotForPostParam: RevertSnapshotForPostParam, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Post>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.revertToSpecifiedSnapshotForPost(name, revertSnapshotForPostParam, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.revertToSpecifiedSnapshotForPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Publish a post.
         * @param {string} name 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async unpublishPost(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Post>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.unpublishPost(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.unpublishPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a post.
         * @param {string} name 
         * @param {PostRequest} postRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateDraftPost(name: string, postRequest: PostRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Post>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateDraftPost(name, postRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.updateDraftPost']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update a post\'s content.
         * @param {string} name 
         * @param {Content} content 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updatePostContent(name: string, content: Content, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Post>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updatePostContent(name, content, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['PostV1alpha1ConsoleApi.updatePostContent']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * PostV1alpha1ConsoleApi - factory interface
 * @export
 */
export const PostV1alpha1ConsoleApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = PostV1alpha1ConsoleApiFp(configuration)
    return {
        /**
         * Delete a content for post.
         * @param {PostV1alpha1ConsoleApiDeletePostContentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deletePostContent(requestParameters: PostV1alpha1ConsoleApiDeletePostContentRequest, options?: RawAxiosRequestConfig): AxiosPromise<ContentWrapper> {
            return localVarFp.deletePostContent(requestParameters.name, requestParameters.snapshotName, options).then((request) => request(axios, basePath));
        },
        /**
         * Draft a post.
         * @param {PostV1alpha1ConsoleApiDraftPostRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        draftPost(requestParameters: PostV1alpha1ConsoleApiDraftPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<Post> {
            return localVarFp.draftPost(requestParameters.postRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * Fetch content of post.
         * @param {PostV1alpha1ConsoleApiFetchPostContentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        fetchPostContent(requestParameters: PostV1alpha1ConsoleApiFetchPostContentRequest, options?: RawAxiosRequestConfig): AxiosPromise<ContentWrapper> {
            return localVarFp.fetchPostContent(requestParameters.name, requestParameters.snapshotName, options).then((request) => request(axios, basePath));
        },
        /**
         * Fetch head content of post.
         * @param {PostV1alpha1ConsoleApiFetchPostHeadContentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        fetchPostHeadContent(requestParameters: PostV1alpha1ConsoleApiFetchPostHeadContentRequest, options?: RawAxiosRequestConfig): AxiosPromise<ContentWrapper> {
            return localVarFp.fetchPostHeadContent(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Fetch release content of post.
         * @param {PostV1alpha1ConsoleApiFetchPostReleaseContentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        fetchPostReleaseContent(requestParameters: PostV1alpha1ConsoleApiFetchPostReleaseContentRequest, options?: RawAxiosRequestConfig): AxiosPromise<ContentWrapper> {
            return localVarFp.fetchPostReleaseContent(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * List all snapshots for post content.
         * @param {PostV1alpha1ConsoleApiListPostSnapshotsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPostSnapshots(requestParameters: PostV1alpha1ConsoleApiListPostSnapshotsRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<ListedSnapshotDto>> {
            return localVarFp.listPostSnapshots(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * List posts.
         * @param {PostV1alpha1ConsoleApiListPostsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPosts(requestParameters: PostV1alpha1ConsoleApiListPostsRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ListedPostList> {
            return localVarFp.listPosts(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, requestParameters.publishPhase, requestParameters.keyword, requestParameters.categoryWithChildren, options).then((request) => request(axios, basePath));
        },
        /**
         * Publish a post.
         * @param {PostV1alpha1ConsoleApiPublishPostRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        publishPost(requestParameters: PostV1alpha1ConsoleApiPublishPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<Post> {
            return localVarFp.publishPost(requestParameters.name, requestParameters.headSnapshot, requestParameters.async, options).then((request) => request(axios, basePath));
        },
        /**
         * Recycle a post.
         * @param {PostV1alpha1ConsoleApiRecyclePostRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        recyclePost(requestParameters: PostV1alpha1ConsoleApiRecyclePostRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.recyclePost(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Revert to specified snapshot for post content.
         * @param {PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPostRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        revertToSpecifiedSnapshotForPost(requestParameters: PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<Post> {
            return localVarFp.revertToSpecifiedSnapshotForPost(requestParameters.name, requestParameters.revertSnapshotForPostParam, options).then((request) => request(axios, basePath));
        },
        /**
         * Publish a post.
         * @param {PostV1alpha1ConsoleApiUnpublishPostRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unpublishPost(requestParameters: PostV1alpha1ConsoleApiUnpublishPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<Post> {
            return localVarFp.unpublishPost(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a post.
         * @param {PostV1alpha1ConsoleApiUpdateDraftPostRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateDraftPost(requestParameters: PostV1alpha1ConsoleApiUpdateDraftPostRequest, options?: RawAxiosRequestConfig): AxiosPromise<Post> {
            return localVarFp.updateDraftPost(requestParameters.name, requestParameters.postRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * Update a post\'s content.
         * @param {PostV1alpha1ConsoleApiUpdatePostContentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePostContent(requestParameters: PostV1alpha1ConsoleApiUpdatePostContentRequest, options?: RawAxiosRequestConfig): AxiosPromise<Post> {
            return localVarFp.updatePostContent(requestParameters.name, requestParameters.content, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for deletePostContent operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiDeletePostContentRequest
 */
export interface PostV1alpha1ConsoleApiDeletePostContentRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiDeletePostContent
     */
    readonly name: string

    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiDeletePostContent
     */
    readonly snapshotName: string
}

/**
 * Request parameters for draftPost operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiDraftPostRequest
 */
export interface PostV1alpha1ConsoleApiDraftPostRequest {
    /**
     * 
     * @type {PostRequest}
     * @memberof PostV1alpha1ConsoleApiDraftPost
     */
    readonly postRequest: PostRequest
}

/**
 * Request parameters for fetchPostContent operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiFetchPostContentRequest
 */
export interface PostV1alpha1ConsoleApiFetchPostContentRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiFetchPostContent
     */
    readonly name: string

    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiFetchPostContent
     */
    readonly snapshotName: string
}

/**
 * Request parameters for fetchPostHeadContent operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiFetchPostHeadContentRequest
 */
export interface PostV1alpha1ConsoleApiFetchPostHeadContentRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiFetchPostHeadContent
     */
    readonly name: string
}

/**
 * Request parameters for fetchPostReleaseContent operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiFetchPostReleaseContentRequest
 */
export interface PostV1alpha1ConsoleApiFetchPostReleaseContentRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiFetchPostReleaseContent
     */
    readonly name: string
}

/**
 * Request parameters for listPostSnapshots operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiListPostSnapshotsRequest
 */
export interface PostV1alpha1ConsoleApiListPostSnapshotsRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiListPostSnapshots
     */
    readonly name: string
}

/**
 * Request parameters for listPosts operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiListPostsRequest
 */
export interface PostV1alpha1ConsoleApiListPostsRequest {
    /**
     * Page number. Default is 0.
     * @type {number}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly page?: number

    /**
     * Size number. Default is 0.
     * @type {number}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly size?: number

    /**
     * Label selector. e.g.: hidden!&#x3D;true
     * @type {Array<string>}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly labelSelector?: Array<string>

    /**
     * Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
     * @type {Array<string>}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly fieldSelector?: Array<string>

    /**
     * Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @type {Array<string>}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly sort?: Array<string>

    /**
     * Posts filtered by publish phase.
     * @type {'DRAFT' | 'PENDING_APPROVAL' | 'PUBLISHED' | 'FAILED'}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly publishPhase?: ListPostsPublishPhaseEnum

    /**
     * Posts filtered by keyword.
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly keyword?: string

    /**
     * Posts filtered by category including sub-categories.
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiListPosts
     */
    readonly categoryWithChildren?: string
}

/**
 * Request parameters for publishPost operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiPublishPostRequest
 */
export interface PostV1alpha1ConsoleApiPublishPostRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiPublishPost
     */
    readonly name: string

    /**
     * Head snapshot name of content.
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiPublishPost
     */
    readonly headSnapshot?: string

    /**
     * 
     * @type {boolean}
     * @memberof PostV1alpha1ConsoleApiPublishPost
     */
    readonly async?: boolean
}

/**
 * Request parameters for recyclePost operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiRecyclePostRequest
 */
export interface PostV1alpha1ConsoleApiRecyclePostRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiRecyclePost
     */
    readonly name: string
}

/**
 * Request parameters for revertToSpecifiedSnapshotForPost operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPostRequest
 */
export interface PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPostRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPost
     */
    readonly name: string

    /**
     * 
     * @type {RevertSnapshotForPostParam}
     * @memberof PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPost
     */
    readonly revertSnapshotForPostParam: RevertSnapshotForPostParam
}

/**
 * Request parameters for unpublishPost operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiUnpublishPostRequest
 */
export interface PostV1alpha1ConsoleApiUnpublishPostRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiUnpublishPost
     */
    readonly name: string
}

/**
 * Request parameters for updateDraftPost operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiUpdateDraftPostRequest
 */
export interface PostV1alpha1ConsoleApiUpdateDraftPostRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiUpdateDraftPost
     */
    readonly name: string

    /**
     * 
     * @type {PostRequest}
     * @memberof PostV1alpha1ConsoleApiUpdateDraftPost
     */
    readonly postRequest: PostRequest
}

/**
 * Request parameters for updatePostContent operation in PostV1alpha1ConsoleApi.
 * @export
 * @interface PostV1alpha1ConsoleApiUpdatePostContentRequest
 */
export interface PostV1alpha1ConsoleApiUpdatePostContentRequest {
    /**
     * 
     * @type {string}
     * @memberof PostV1alpha1ConsoleApiUpdatePostContent
     */
    readonly name: string

    /**
     * 
     * @type {Content}
     * @memberof PostV1alpha1ConsoleApiUpdatePostContent
     */
    readonly content: Content
}

/**
 * PostV1alpha1ConsoleApi - object-oriented interface
 * @export
 * @class PostV1alpha1ConsoleApi
 * @extends {BaseAPI}
 */
export class PostV1alpha1ConsoleApi extends BaseAPI {
    /**
     * Delete a content for post.
     * @param {PostV1alpha1ConsoleApiDeletePostContentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public deletePostContent(requestParameters: PostV1alpha1ConsoleApiDeletePostContentRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).deletePostContent(requestParameters.name, requestParameters.snapshotName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Draft a post.
     * @param {PostV1alpha1ConsoleApiDraftPostRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public draftPost(requestParameters: PostV1alpha1ConsoleApiDraftPostRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).draftPost(requestParameters.postRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Fetch content of post.
     * @param {PostV1alpha1ConsoleApiFetchPostContentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public fetchPostContent(requestParameters: PostV1alpha1ConsoleApiFetchPostContentRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).fetchPostContent(requestParameters.name, requestParameters.snapshotName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Fetch head content of post.
     * @param {PostV1alpha1ConsoleApiFetchPostHeadContentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public fetchPostHeadContent(requestParameters: PostV1alpha1ConsoleApiFetchPostHeadContentRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).fetchPostHeadContent(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Fetch release content of post.
     * @param {PostV1alpha1ConsoleApiFetchPostReleaseContentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public fetchPostReleaseContent(requestParameters: PostV1alpha1ConsoleApiFetchPostReleaseContentRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).fetchPostReleaseContent(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * List all snapshots for post content.
     * @param {PostV1alpha1ConsoleApiListPostSnapshotsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public listPostSnapshots(requestParameters: PostV1alpha1ConsoleApiListPostSnapshotsRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).listPostSnapshots(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * List posts.
     * @param {PostV1alpha1ConsoleApiListPostsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public listPosts(requestParameters: PostV1alpha1ConsoleApiListPostsRequest = {}, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).listPosts(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, requestParameters.publishPhase, requestParameters.keyword, requestParameters.categoryWithChildren, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Publish a post.
     * @param {PostV1alpha1ConsoleApiPublishPostRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public publishPost(requestParameters: PostV1alpha1ConsoleApiPublishPostRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).publishPost(requestParameters.name, requestParameters.headSnapshot, requestParameters.async, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Recycle a post.
     * @param {PostV1alpha1ConsoleApiRecyclePostRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public recyclePost(requestParameters: PostV1alpha1ConsoleApiRecyclePostRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).recyclePost(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Revert to specified snapshot for post content.
     * @param {PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPostRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public revertToSpecifiedSnapshotForPost(requestParameters: PostV1alpha1ConsoleApiRevertToSpecifiedSnapshotForPostRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).revertToSpecifiedSnapshotForPost(requestParameters.name, requestParameters.revertSnapshotForPostParam, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Publish a post.
     * @param {PostV1alpha1ConsoleApiUnpublishPostRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public unpublishPost(requestParameters: PostV1alpha1ConsoleApiUnpublishPostRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).unpublishPost(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a post.
     * @param {PostV1alpha1ConsoleApiUpdateDraftPostRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public updateDraftPost(requestParameters: PostV1alpha1ConsoleApiUpdateDraftPostRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).updateDraftPost(requestParameters.name, requestParameters.postRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update a post\'s content.
     * @param {PostV1alpha1ConsoleApiUpdatePostContentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PostV1alpha1ConsoleApi
     */
    public updatePostContent(requestParameters: PostV1alpha1ConsoleApiUpdatePostContentRequest, options?: RawAxiosRequestConfig) {
        return PostV1alpha1ConsoleApiFp(this.configuration).updatePostContent(requestParameters.name, requestParameters.content, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const ListPostsPublishPhaseEnum = {
    Draft: 'DRAFT',
    PendingApproval: 'PENDING_APPROVAL',
    Published: 'PUBLISHED',
    Failed: 'FAILED'
} as const;
export type ListPostsPublishPhaseEnum = typeof ListPostsPublishPhaseEnum[keyof typeof ListPostsPublishPhaseEnum];
