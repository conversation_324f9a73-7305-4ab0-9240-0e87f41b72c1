/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import { JsonPatchInner } from '../models';
// @ts-ignore
import { Setting } from '../models';
// @ts-ignore
import { SettingList } from '../models';
/**
 * SettingV1alpha1Api - axios parameter creator
 * @export
 */
export const SettingV1alpha1ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Create Setting
         * @param {Setting} [setting] Fresh setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSetting: async (setting?: Setting, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1alpha1/settings`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(setting, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Delete Setting
         * @param {string} name Name of setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSetting: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('deleteSetting', 'name', name)
            const localVarPath = `/api/v1alpha1/settings/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Get Setting
         * @param {string} name Name of setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSetting: async (name: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('getSetting', 'name', name)
            const localVarPath = `/api/v1alpha1/settings/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * List Setting
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listSetting: async (page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1alpha1/settings`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (labelSelector) {
                localVarQueryParameter['labelSelector'] = labelSelector;
            }

            if (fieldSelector) {
                localVarQueryParameter['fieldSelector'] = fieldSelector;
            }

            if (sort) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Patch Setting
         * @param {string} name Name of setting
         * @param {Array<JsonPatchInner>} [jsonPatchInner] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        patchSetting: async (name: string, jsonPatchInner?: Array<JsonPatchInner>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('patchSetting', 'name', name)
            const localVarPath = `/api/v1alpha1/settings/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(jsonPatchInner, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Update Setting
         * @param {string} name Name of setting
         * @param {Setting} [setting] Updated setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSetting: async (name: string, setting?: Setting, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'name' is not null or undefined
            assertParamExists('updateSetting', 'name', name)
            const localVarPath = `/api/v1alpha1/settings/{name}`
                .replace(`{${"name"}}`, encodeURIComponent(String(name)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication basicAuth required
            // http basic authentication required
            setBasicAuthToObject(localVarRequestOptions, configuration)

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(setting, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SettingV1alpha1Api - functional programming interface
 * @export
 */
export const SettingV1alpha1ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = SettingV1alpha1ApiAxiosParamCreator(configuration)
    return {
        /**
         * Create Setting
         * @param {Setting} [setting] Fresh setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSetting(setting?: Setting, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Setting>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSetting(setting, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SettingV1alpha1Api.createSetting']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Delete Setting
         * @param {string} name Name of setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteSetting(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteSetting(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SettingV1alpha1Api.deleteSetting']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Get Setting
         * @param {string} name Name of setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSetting(name: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Setting>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSetting(name, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SettingV1alpha1Api.getSetting']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * List Setting
         * @param {number} [page] Page number. Default is 0.
         * @param {number} [size] Size number. Default is 0.
         * @param {Array<string>} [labelSelector] Label selector. e.g.: hidden!&#x3D;true
         * @param {Array<string>} [fieldSelector] Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
         * @param {Array<string>} [sort] Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listSetting(page?: number, size?: number, labelSelector?: Array<string>, fieldSelector?: Array<string>, sort?: Array<string>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<SettingList>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listSetting(page, size, labelSelector, fieldSelector, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SettingV1alpha1Api.listSetting']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Patch Setting
         * @param {string} name Name of setting
         * @param {Array<JsonPatchInner>} [jsonPatchInner] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async patchSetting(name: string, jsonPatchInner?: Array<JsonPatchInner>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Setting>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.patchSetting(name, jsonPatchInner, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SettingV1alpha1Api.patchSetting']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Update Setting
         * @param {string} name Name of setting
         * @param {Setting} [setting] Updated setting
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSetting(name: string, setting?: Setting, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Setting>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSetting(name, setting, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['SettingV1alpha1Api.updateSetting']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * SettingV1alpha1Api - factory interface
 * @export
 */
export const SettingV1alpha1ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = SettingV1alpha1ApiFp(configuration)
    return {
        /**
         * Create Setting
         * @param {SettingV1alpha1ApiCreateSettingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSetting(requestParameters: SettingV1alpha1ApiCreateSettingRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<Setting> {
            return localVarFp.createSetting(requestParameters.setting, options).then((request) => request(axios, basePath));
        },
        /**
         * Delete Setting
         * @param {SettingV1alpha1ApiDeleteSettingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSetting(requestParameters: SettingV1alpha1ApiDeleteSettingRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteSetting(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * Get Setting
         * @param {SettingV1alpha1ApiGetSettingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSetting(requestParameters: SettingV1alpha1ApiGetSettingRequest, options?: RawAxiosRequestConfig): AxiosPromise<Setting> {
            return localVarFp.getSetting(requestParameters.name, options).then((request) => request(axios, basePath));
        },
        /**
         * List Setting
         * @param {SettingV1alpha1ApiListSettingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listSetting(requestParameters: SettingV1alpha1ApiListSettingRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<SettingList> {
            return localVarFp.listSetting(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * Patch Setting
         * @param {SettingV1alpha1ApiPatchSettingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        patchSetting(requestParameters: SettingV1alpha1ApiPatchSettingRequest, options?: RawAxiosRequestConfig): AxiosPromise<Setting> {
            return localVarFp.patchSetting(requestParameters.name, requestParameters.jsonPatchInner, options).then((request) => request(axios, basePath));
        },
        /**
         * Update Setting
         * @param {SettingV1alpha1ApiUpdateSettingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSetting(requestParameters: SettingV1alpha1ApiUpdateSettingRequest, options?: RawAxiosRequestConfig): AxiosPromise<Setting> {
            return localVarFp.updateSetting(requestParameters.name, requestParameters.setting, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createSetting operation in SettingV1alpha1Api.
 * @export
 * @interface SettingV1alpha1ApiCreateSettingRequest
 */
export interface SettingV1alpha1ApiCreateSettingRequest {
    /**
     * Fresh setting
     * @type {Setting}
     * @memberof SettingV1alpha1ApiCreateSetting
     */
    readonly setting?: Setting
}

/**
 * Request parameters for deleteSetting operation in SettingV1alpha1Api.
 * @export
 * @interface SettingV1alpha1ApiDeleteSettingRequest
 */
export interface SettingV1alpha1ApiDeleteSettingRequest {
    /**
     * Name of setting
     * @type {string}
     * @memberof SettingV1alpha1ApiDeleteSetting
     */
    readonly name: string
}

/**
 * Request parameters for getSetting operation in SettingV1alpha1Api.
 * @export
 * @interface SettingV1alpha1ApiGetSettingRequest
 */
export interface SettingV1alpha1ApiGetSettingRequest {
    /**
     * Name of setting
     * @type {string}
     * @memberof SettingV1alpha1ApiGetSetting
     */
    readonly name: string
}

/**
 * Request parameters for listSetting operation in SettingV1alpha1Api.
 * @export
 * @interface SettingV1alpha1ApiListSettingRequest
 */
export interface SettingV1alpha1ApiListSettingRequest {
    /**
     * Page number. Default is 0.
     * @type {number}
     * @memberof SettingV1alpha1ApiListSetting
     */
    readonly page?: number

    /**
     * Size number. Default is 0.
     * @type {number}
     * @memberof SettingV1alpha1ApiListSetting
     */
    readonly size?: number

    /**
     * Label selector. e.g.: hidden!&#x3D;true
     * @type {Array<string>}
     * @memberof SettingV1alpha1ApiListSetting
     */
    readonly labelSelector?: Array<string>

    /**
     * Field selector. e.g.: metadata.name&#x3D;&#x3D;halo
     * @type {Array<string>}
     * @memberof SettingV1alpha1ApiListSetting
     */
    readonly fieldSelector?: Array<string>

    /**
     * Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.
     * @type {Array<string>}
     * @memberof SettingV1alpha1ApiListSetting
     */
    readonly sort?: Array<string>
}

/**
 * Request parameters for patchSetting operation in SettingV1alpha1Api.
 * @export
 * @interface SettingV1alpha1ApiPatchSettingRequest
 */
export interface SettingV1alpha1ApiPatchSettingRequest {
    /**
     * Name of setting
     * @type {string}
     * @memberof SettingV1alpha1ApiPatchSetting
     */
    readonly name: string

    /**
     * 
     * @type {Array<JsonPatchInner>}
     * @memberof SettingV1alpha1ApiPatchSetting
     */
    readonly jsonPatchInner?: Array<JsonPatchInner>
}

/**
 * Request parameters for updateSetting operation in SettingV1alpha1Api.
 * @export
 * @interface SettingV1alpha1ApiUpdateSettingRequest
 */
export interface SettingV1alpha1ApiUpdateSettingRequest {
    /**
     * Name of setting
     * @type {string}
     * @memberof SettingV1alpha1ApiUpdateSetting
     */
    readonly name: string

    /**
     * Updated setting
     * @type {Setting}
     * @memberof SettingV1alpha1ApiUpdateSetting
     */
    readonly setting?: Setting
}

/**
 * SettingV1alpha1Api - object-oriented interface
 * @export
 * @class SettingV1alpha1Api
 * @extends {BaseAPI}
 */
export class SettingV1alpha1Api extends BaseAPI {
    /**
     * Create Setting
     * @param {SettingV1alpha1ApiCreateSettingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SettingV1alpha1Api
     */
    public createSetting(requestParameters: SettingV1alpha1ApiCreateSettingRequest = {}, options?: RawAxiosRequestConfig) {
        return SettingV1alpha1ApiFp(this.configuration).createSetting(requestParameters.setting, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Delete Setting
     * @param {SettingV1alpha1ApiDeleteSettingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SettingV1alpha1Api
     */
    public deleteSetting(requestParameters: SettingV1alpha1ApiDeleteSettingRequest, options?: RawAxiosRequestConfig) {
        return SettingV1alpha1ApiFp(this.configuration).deleteSetting(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Get Setting
     * @param {SettingV1alpha1ApiGetSettingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SettingV1alpha1Api
     */
    public getSetting(requestParameters: SettingV1alpha1ApiGetSettingRequest, options?: RawAxiosRequestConfig) {
        return SettingV1alpha1ApiFp(this.configuration).getSetting(requestParameters.name, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * List Setting
     * @param {SettingV1alpha1ApiListSettingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SettingV1alpha1Api
     */
    public listSetting(requestParameters: SettingV1alpha1ApiListSettingRequest = {}, options?: RawAxiosRequestConfig) {
        return SettingV1alpha1ApiFp(this.configuration).listSetting(requestParameters.page, requestParameters.size, requestParameters.labelSelector, requestParameters.fieldSelector, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Patch Setting
     * @param {SettingV1alpha1ApiPatchSettingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SettingV1alpha1Api
     */
    public patchSetting(requestParameters: SettingV1alpha1ApiPatchSettingRequest, options?: RawAxiosRequestConfig) {
        return SettingV1alpha1ApiFp(this.configuration).patchSetting(requestParameters.name, requestParameters.jsonPatchInner, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Update Setting
     * @param {SettingV1alpha1ApiUpdateSettingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SettingV1alpha1Api
     */
    public updateSetting(requestParameters: SettingV1alpha1ApiUpdateSettingRequest, options?: RawAxiosRequestConfig) {
        return SettingV1alpha1ApiFp(this.configuration).updateSetting(requestParameters.name, requestParameters.setting, options).then((request) => request(this.axios, this.basePath));
    }
}

