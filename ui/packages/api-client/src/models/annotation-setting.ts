/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { AnnotationSettingSpec } from './annotation-setting-spec';
// May contain unused imports in some cases
// @ts-ignore
import { Metadata } from './metadata';

/**
 * 
 * @export
 * @interface AnnotationSetting
 */
export interface AnnotationSetting {
    /**
     * 
     * @type {string}
     * @memberof AnnotationSetting
     */
    'apiVersion': string;
    /**
     * 
     * @type {string}
     * @memberof AnnotationSetting
     */
    'kind': string;
    /**
     * 
     * @type {Metadata}
     * @memberof AnnotationSetting
     */
    'metadata': Metadata;
    /**
     * 
     * @type {AnnotationSettingSpec}
     * @memberof AnnotationSetting
     */
    'spec': AnnotationSettingSpec;
}

