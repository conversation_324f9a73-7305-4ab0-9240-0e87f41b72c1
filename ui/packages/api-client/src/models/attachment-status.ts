/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface AttachmentStatus
 */
export interface AttachmentStatus {
    /**
     * Permalink of attachment. If it is in local storage, the public URL will be set. If it is in s3 storage, the Object URL will be set. 
     * @type {string}
     * @memberof AttachmentStatus
     */
    'permalink'?: string;
    /**
     * 
     * @type {{ [key: string]: string; }}
     * @memberof AttachmentStatus
     */
    'thumbnails'?: { [key: string]: string; };
}

