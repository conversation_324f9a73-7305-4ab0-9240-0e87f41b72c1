/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { Comment } from './comment';
// May contain unused imports in some cases
// @ts-ignore
import { CommentStats } from './comment-stats';
// May contain unused imports in some cases
// @ts-ignore
import { Extension } from './extension';
// May contain unused imports in some cases
// @ts-ignore
import { OwnerInfo } from './owner-info';

/**
 * A chunk of items.
 * @export
 * @interface ListedComment
 */
export interface ListedComment {
    /**
     * 
     * @type {Comment}
     * @memberof ListedComment
     */
    'comment': Comment;
    /**
     * 
     * @type {OwnerInfo}
     * @memberof ListedComment
     */
    'owner': OwnerInfo;
    /**
     * 
     * @type {CommentStats}
     * @memberof ListedComment
     */
    'stats': CommentStats;
    /**
     * 
     * @type {Extension}
     * @memberof ListedComment
     */
    'subject'?: Extension;
}

