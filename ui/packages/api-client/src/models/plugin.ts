/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { Metadata } from './metadata';
// May contain unused imports in some cases
// @ts-ignore
import { PluginSpec } from './plugin-spec';
// May contain unused imports in some cases
// @ts-ignore
import { PluginStatus } from './plugin-status';

/**
 * A custom resource for Plugin.
 * @export
 * @interface Plugin
 */
export interface Plugin {
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'apiVersion': string;
    /**
     * 
     * @type {string}
     * @memberof Plugin
     */
    'kind': string;
    /**
     * 
     * @type {Metadata}
     * @memberof Plugin
     */
    'metadata': Metadata;
    /**
     * 
     * @type {PluginSpec}
     * @memberof Plugin
     */
    'spec': PluginSpec;
    /**
     * 
     * @type {PluginStatus}
     * @memberof Plugin
     */
    'status'?: PluginStatus;
}

