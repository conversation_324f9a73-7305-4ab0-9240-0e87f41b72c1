/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { Metadata } from './metadata';
// May contain unused imports in some cases
// @ts-ignore
import { ReasonTypeSpec } from './reason-type-spec';

/**
 * <p>{@link ReasonType ReasonType} is a custom extension that defines a type of reason.</p>  <p>One {@link ReasonType ReasonType} can have multiple {@link Reason Reason}s to notify.</p>
 * @export
 * @interface ReasonType
 */
export interface ReasonType {
    /**
     * 
     * @type {string}
     * @memberof ReasonType
     */
    'apiVersion': string;
    /**
     * 
     * @type {string}
     * @memberof ReasonType
     */
    'kind': string;
    /**
     * 
     * @type {Metadata}
     * @memberof ReasonType
     */
    'metadata': Metadata;
    /**
     * 
     * @type {ReasonTypeSpec}
     * @memberof ReasonType
     */
    'spec'?: ReasonTypeSpec;
}

