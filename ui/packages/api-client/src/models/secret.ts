/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { Metadata } from './metadata';

/**
 * Secret is a small piece of sensitive data which should be kept secret, such as a password,  a token, or a key.
 * @export
 * @interface Secret
 */
export interface Secret {
    /**
     * 
     * @type {string}
     * @memberof Secret
     */
    'apiVersion': string;
    /**
     * <p>The total bytes of the values in  the Data field must be less than {@link run.halo.app.extension.Secret#MAX_SECRET_SIZE #MAX_SECRET_SIZE} bytes.</p>  <p><code>data</code> contains the secret data. Each key must consist of alphanumeric  characters, \'-\', \'_\' or \'.\'. The serialized form of the secret data is a  base64 encoded string, representing the arbitrary (possibly non-string)  data value here. Described in  <a href=\"https://tools.ietf.org/html/rfc4648#section-4\">rfc4648#section-4</a>  </p>
     * @type {{ [key: string]: string; }}
     * @memberof Secret
     */
    'data'?: { [key: string]: string; };
    /**
     * 
     * @type {string}
     * @memberof Secret
     */
    'kind': string;
    /**
     * 
     * @type {Metadata}
     * @memberof Secret
     */
    'metadata': Metadata;
    /**
     * <code>stringData</code> allows specifying non-binary secret data in string form.  It is provided as a write-only input field for convenience.  All keys and values are merged into the data field on write, overwriting any existing  values.  The stringData field is never output when reading from the API.
     * @type {{ [key: string]: string; }}
     * @memberof Secret
     */
    'stringData'?: { [key: string]: string; };
    /**
     * Used to facilitate programmatic handling of secret data.  More info:  <a href=\"https://kubernetes.io/docs/concepts/configuration/secret/#secret-types\">secret-types</a>
     * @type {string}
     * @memberof Secret
     */
    'type'?: string;
}

