/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { Setting } from './setting';

/**
 * 
 * @export
 * @interface SettingList
 */
export interface SettingList {
    /**
     * Indicates whether current page is the first page.
     * @type {boolean}
     * @memberof SettingList
     */
    'first': boolean;
    /**
     * Indicates whether current page has previous page.
     * @type {boolean}
     * @memberof SettingList
     */
    'hasNext': boolean;
    /**
     * Indicates whether current page has previous page.
     * @type {boolean}
     * @memberof SettingList
     */
    'hasPrevious': boolean;
    /**
     * A chunk of items.
     * @type {Array<Setting>}
     * @memberof SettingList
     */
    'items': Array<Setting>;
    /**
     * Indicates whether current page is the last page.
     * @type {boolean}
     * @memberof SettingList
     */
    'last': boolean;
    /**
     * Page number, starts from 1. If not set or equal to 0, it means no pagination.
     * @type {number}
     * @memberof SettingList
     */
    'page': number;
    /**
     * Size of each page. If not set or equal to 0, it means no pagination.
     * @type {number}
     * @memberof SettingList
     */
    'size': number;
    /**
     * Total elements.
     * @type {number}
     * @memberof SettingList
     */
    'total': number;
    /**
     * Indicates total pages.
     * @type {number}
     * @memberof SettingList
     */
    'totalPages': number;
}

