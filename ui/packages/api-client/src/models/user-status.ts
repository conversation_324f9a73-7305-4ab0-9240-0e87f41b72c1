/* tslint:disable */
/* eslint-disable */
/**
 * Halo
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 2.21.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { LoginHistory } from './login-history';

/**
 * 
 * @export
 * @interface UserStatus
 */
export interface UserStatus {
    /**
     * 
     * @type {string}
     * @memberof UserStatus
     */
    'lastLoginAt'?: string;
    /**
     * 
     * @type {Array<LoginHistory>}
     * @memberof UserStatus
     */
    'loginHistories'?: Array<LoginHistory>;
    /**
     * 
     * @type {string}
     * @memberof UserStatus
     */
    'permalink'?: string;
}

