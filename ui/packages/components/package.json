{"name": "@halo-dev/components", "version": "2.21.0", "description": "", "keywords": ["halo", "halo-components", "halo-dev", "@halo-dev/components"], "homepage": "https://github.com/halo-dev/halo/tree/main/ui/packages/components#readme", "bugs": {"url": "https://github.com/halo-dev/halo/issues"}, "repository": {"type": "git", "url": "https://github.com/halo-dev/halo.git", "directory": "ui/packages/components"}, "license": "MIT", "author": {"name": "@halo-dev", "url": "https://github.com/halo-dev"}, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/halo-components.es.js"}, "./dist/style.css": "./dist/style.css"}, "main": "./dist/halo-components.iife.js", "jsdelivr": "./dist/halo-components.iife.js", "unpkg": "./dist/halo-components.iife.js", "module": "./dist/halo-components.es.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "build-storybook": "storybook build", "dev": "vite build --watch", "storybook": "storybook dev -p 6006", "test:unit": "vitest --run", "test:unit:coverage": "vitest run --coverage", "test:unit:ui": "vitest --watch --ui", "test:unit:watch": "vitest --watch", "typecheck": "vue-tsc --noEmit -p tsconfig.app.json --composite false"}, "dependencies": {"floating-vue": "^5.2.2"}, "devDependencies": {"@storybook/addon-essentials": "^7.6.3", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/addon-styling": "^1.3.7", "@storybook/blocks": "^7.6.3", "@storybook/testing-library": "^0.0.14-next.2", "@storybook/vue3": "^7.6.3", "@storybook/vue3-vite": "^7.6.3", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook": "^7.6.3"}, "peerDependencies": {"vue": "^3.5.16", "vue-router": "^4.5.1"}}