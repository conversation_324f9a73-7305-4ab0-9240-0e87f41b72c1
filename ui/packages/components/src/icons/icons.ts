import IconAccountCircleLine from "~icons/ri/account-circle-line";
import IconAddCircle from "~icons/ri/add-circle-line";
import IconArrowDownCircleLine from "~icons/ri/arrow-down-circle-line";
import IconArrowDown from "~icons/ri/arrow-down-s-fill";
import IconArrowDownLine from "~icons/ri/arrow-down-s-line";
import IconArrowLeftRightLine from "~icons/ri/arrow-left-right-line";
import IconArrowLeft from "~icons/ri/arrow-left-s-line";
import IconArrowRight from "~icons/ri/arrow-right-s-line";
import IconArrowUpCircleLine from "~icons/ri/arrow-up-circle-line";
import IconArrowUpDownLine from "~icons/ri/arrow-up-down-line";
import IconArrowUpLine from "~icons/ri/arrow-up-s-line";
import IconBookRead from "~icons/ri/book-read-line";
import IconCalendar from "~icons/ri/calendar-line";
import IconCharacterRecognition from "~icons/ri/character-recognition-line";
import IconCheckboxFill from "~icons/ri/checkbox-circle-fill";
import IconCheckboxCircle from "~icons/ri/checkbox-circle-line";
import IconClipboardLine from "~icons/ri/clipboard-line";
import IconCloseCircle from "~icons/ri/close-circle-line";
import IconClose from "~icons/ri/close-line";
import IconCodeBoxLine from "~icons/ri/code-box-line";
import IconComputer from "~icons/ri/computer-line";
import IconDashboard from "~icons/ri/dashboard-3-line";
import IconDatabase2Line from "~icons/ri/database-2-line";
import IconDeleteBin from "~icons/ri/delete-bin-2-line";
import IconMotionLine from "~icons/ri/emotion-line";
import IconErrorWarning from "~icons/ri/error-warning-line";
import IconExchange from "~icons/ri/exchange-line";
import IconExternalLinkLine from "~icons/ri/external-link-line";
import IconEye from "~icons/ri/eye-line";
import IconEyeOff from "~icons/ri/eye-off-line";
import IconFolder from "~icons/ri/folder-2-line";
import IconForbidLine from "~icons/ri/forbid-line";
import IconGitBranch from "~icons/ri/git-branch-line";
import IconGitHub from "~icons/ri/github-fill";
import IconGrid from "~icons/ri/grid-line";
import IconHistoryLine from "~icons/ri/history-line";
import IconImageAddLine from "~icons/ri/image-add-line";
import IconInformation from "~icons/ri/information-line";
import IconLink from "~icons/ri/link";
import IconListSettings from "~icons/ri/list-settings-line";
import IconList from "~icons/ri/list-unordered";
import IconLockPasswordLine from "~icons/ri/lock-password-line";
import IconLogoutCircleRLine from "~icons/ri/logout-circle-r-line";
import IconMagic from "~icons/ri/magic-line";
import IconMessage from "~icons/ri/message-3-line";
import IconMore from "~icons/ri/more-line";
import IconNotificationBadgeLine from "~icons/ri/notification-badge-line";
import IconPages from "~icons/ri/pages-line";
import IconPalette from "~icons/ri/palette-line";
import IconRiPencilFill from "~icons/ri/pencil-fill";
import IconPlug from "~icons/ri/plug-2-line";
import IconRefreshLine from "~icons/ri/refresh-line";
import IconReplyLine from "~icons/ri/reply-line";
import IconRocketLine from "~icons/ri/rocket-line";
import IconSave from "~icons/ri/save-line";
import IconSearch from "~icons/ri/search-2-line";
import IconSendPlaneFill from "~icons/ri/send-plane-fill";
import IconServerLine from "~icons/ri/server-line";
import IconSettings3Line from "~icons/ri/settings-3-line";
import IconSettings from "~icons/ri/settings-4-line";
import IconShieldUser from "~icons/ri/shield-user-line";
import IconPhone from "~icons/ri/smartphone-line";
import IconStopCircle from "~icons/ri/stop-circle-line";
import IconTablet from "~icons/ri/tablet-line";
import IconTeam from "~icons/ri/team-fill";
import IconTerminalBoxLine from "~icons/ri/terminal-box-line";
import IconTimerLine from "~icons/ri/timer-line";
import IconToolsFill from "~icons/ri/tools-fill";
import IconRiUpload2Fill from "~icons/ri/upload-2-fill";
import IconUpload from "~icons/ri/upload-cloud-2-line";
import IconUserFollow from "~icons/ri/user-follow-line";
import IconUserLine from "~icons/ri/user-line";
import IconUserSettings from "~icons/ri/user-settings-line";
import IconWindowLine from "~icons/ri/window-line";
import IconZoomInLine from "~icons/ri/zoom-in-line";
import IconZoomOutLine from "~icons/ri/zoom-out-line";

export {
  IconAccountCircleLine,
  IconAddCircle,
  IconArrowDown,
  IconArrowDownCircleLine,
  IconArrowDownLine,
  IconArrowLeft,
  IconArrowLeftRightLine,
  IconArrowRight,
  IconArrowUpCircleLine,
  IconArrowUpDownLine,
  IconArrowUpLine,
  IconBookRead,
  IconCalendar,
  IconCharacterRecognition,
  IconCheckboxCircle,
  IconCheckboxFill,
  IconClipboardLine,
  IconClose,
  IconCloseCircle,
  IconCodeBoxLine,
  IconComputer,
  IconDashboard,
  IconDatabase2Line,
  IconDeleteBin,
  IconErrorWarning,
  IconExchange,
  IconExternalLinkLine,
  IconEye,
  IconEyeOff,
  IconFolder,
  IconForbidLine,
  IconGitBranch,
  IconGitHub,
  IconGrid,
  IconHistoryLine,
  IconImageAddLine,
  IconInformation,
  IconLink,
  IconList,
  IconListSettings,
  IconLockPasswordLine,
  IconLogoutCircleRLine,
  IconMagic,
  IconMessage,
  IconMore,
  IconMotionLine,
  IconNotificationBadgeLine,
  IconPages,
  IconPalette,
  IconPhone,
  IconPlug,
  IconRefreshLine,
  IconReplyLine,
  IconRiPencilFill,
  IconRiUpload2Fill,
  IconRocketLine,
  IconSave,
  IconSearch,
  IconSendPlaneFill,
  IconServerLine,
  IconSettings,
  IconSettings3Line,
  IconShieldUser,
  IconStopCircle,
  IconTablet,
  IconTeam,
  IconTerminalBoxLine,
  IconTimerLine,
  IconToolsFill,
  IconUpload,
  IconUserFollow,
  IconUserLine,
  IconUserSettings,
  IconWindowLine,
  IconZoomInLine,
  IconZoomOutLine,
};
