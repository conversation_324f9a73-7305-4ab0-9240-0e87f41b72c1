<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Salvo Demo</title>
  </head>
  <body id="body" class="bg-gradient-to-br from-blue-400 via-teal-400 to-green-500 min-h-screen">
    <div x-data="loginForm()">
        <div class="flex min-h-screen items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div class="w-full max-w-md space-y-6 bg-white bg-opacity-80 p-10 rounded-3xl shadow-xl border border-blue-200">
            <div>
              <h2 class="text-center text-4xl font-extrabold tracking-tight text-blue-900">
                登录
              </h2>
              <p class="mt-2 text-center text-sm text-gray-600">
                由 Salvo-Cli 生成，使用前请阅读 README.md
              </p>
            </div>
            <form class="mt-8 space-y-6" @submit.prevent="submit">
              <input type="hidden" name="remember" value="true" />
              <div class="space-y-4">
                <div>
                  <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                  <input
                    x-model="username"
                    id="username"
                    name="username"
                    type="text"
                    autocomplete="username"
                    required
                    class="block w-full appearance-none rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-400 focus:border-teal-500 focus:outline-none focus:ring-2 focus:ring-teal-300 sm:text-sm transition"
                    placeholder="用户名"
                  />
                </div>
                <div>
                  <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                  <input
                    x-model="password"
                    id="password"
                    name="password"
                    type="password"
                    autocomplete="current-password"
                    required
                    class="block w-full appearance-none rounded-lg border border-gray-300 px-4 py-3 text-gray-900 placeholder-gray-400 focus:border-teal-500 focus:outline-none focus:ring-2 focus:ring-teal-300 sm:text-sm transition"
                    placeholder="密码"
                  />
                </div>
              </div>
              
              <div>
                <button
                  type="submit"
                  class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  登录
                </button>
              </div>
            </form>
            <div class="text-center text-xs text-gray-500 mt-6">
              <p>账号: zhangsan</p>
              <p>密码: 123</p>
            </div>
          </div>
        </div>
    </div>
  </body>
  <script src="assets/js/tailwindcss.js" defer></script>
  <script src="assets/js/sweetalert2.js" defer></script>
  <script src="assets/js/alpinejs.js" defer></script>
  <script>
    function loginForm() {
      return {
        username: "",
        password: "",
        userList: "",
        async submit() {
          try {
            const response = await fetch("/api/login", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "accept": "application/json",
              },
              body: JSON.stringify({
                username: this.username,
                password: this.password,
              }),
            });
            if (!response.ok) {
              const data = await response.json();
              throw new Error(`${data.error.brief}`);
            }
            window.location.href = "/users";
          } catch (error) {
            Swal.fire({
              title: "Error!",
              text: error.message,
              icon: "error",
              confirmButtonText: "OK",
            });
          }
        },
      };
    }
  </script>
</html>
