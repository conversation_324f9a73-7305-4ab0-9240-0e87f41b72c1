<div id="app" x-data="userForm" x-init="fetchData()" class="bg-gradient-to-br from-blue-400 via-teal-400 to-green-500 min-h-screen">
  <div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-base font-semibold leading-6 text-gray-900">用户列表</h1>
      </div>
      <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
        <button
          @click="addUser()"
          class="block rounded-full bg-gradient-to-r from-green-400 to-blue-500 px-4 py-2 text-center text-sm font-semibold text-white shadow-lg hover:from-green-300 hover:to-blue-400 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-green-500 transform transition duration-150 ease-in-out"
        >
          添加用户
        </button>
      </div>
    </div>
    <div class="mt-4">
      <div class="flex gap-4 items-center">
        <input
          type="text"
          x-model="searchUsername"
          @keyup.enter="search()"
          placeholder="搜索用户名..."
          class="block w-64 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 px-3"
        />
        <button
          @click="search()"
          class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          搜索
        </button>
      </div>
    </div>
    <div class="mt-8 flow-root">
      <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
          <table class="min-w-full divide-y divide-gray-300 rounded-lg">
            <thead>
              <tr>
                <th
                  scope="col"
                  class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0 rounded-lg"
                >用户名</th>
                <th
                  scope="col"
                  class="relative py-3.5 pl-3 pr-4 text-right sm:pr-0 rounded-lg"
                >操作</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <template x-for="user in users" :key="user.id">
                <tr>
                  <td
                    class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0 rounded-lg"
                    x-text="user.username"
                  ></td>
                  <td
                    class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0 rounded-lg"
                  >
                    <div class="flex justify-end space-x-3">
                      <a
                        href="#"
                        class="text-indigo-600 hover:text-indigo-900 rounded-full px-3 py-1 bg-indigo-100 hover:bg-indigo-200 transition-colors"
                        @click.prevent="updateUser(user.id, user.username)"
                      >更新</a>
                      <a
                        href="#"
                        class="text-red-600 hover:text-red-900 rounded-full px-3 py-1 bg-red-100 hover:bg-red-200 transition-colors"
                        @click.prevent="deleteUser(user.id)"
                      >删除</a>
                    </div>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="mt-4 flex items-center justify-between">
      <div class="text-sm text-gray-700">
        <span x-text="'共 %{count} 条记录'.replace('%{count}', total)"></span>
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="prevPage()"
          :disabled="currentPage <= 1"
          :class="{'opacity-50 cursor-not-allowed': currentPage <= 1}"
          class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
        >
          上一页
        </button>
        <span class="text-sm text-gray-700" x-text="'第 %{number} 页'.replace('%{number}', currentPage)"></span>
        <button
          @click="nextPage()"
          :disabled="currentPage >= Math.ceil(total / pageSize)"
          :class="{'opacity-50 cursor-not-allowed': currentPage >= Math.ceil(total / pageSize)}"
          class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</div>